# Authentication Implementation

name: "Authentication Implementation"
description: |
  Comprehensive authentication system for CCL platform supporting multiple authentication methods, secure session management, and enterprise SSO integration.
  
  Core Principles:
  - **Multi-Factor Security**: Support 2FA/MFA for enhanced security
  - **Enterprise Ready**: SAML and OIDC support for SSO
  - **Secure Defaults**: Strong password policies and secure tokens
  - **Audit Trail**: Complete authentication event logging
  - **Scalable**: Support for millions of users

## Goal

Implement a robust authentication system that securely verifies user identities while providing seamless login experiences across web, mobile, and API access patterns.

## Why

Authentication is critical for:
- Protecting user accounts and data
- Enabling personalized experiences
- Meeting enterprise security requirements
- Compliance with regulations (SOC2, HIPAA)
- Preventing unauthorized access

This provides:
- Secure user verification
- Scalable authentication architecture
- Enterprise SSO capabilities
- Multi-factor authentication
- Session management

## What

### User-Visible Behavior
- Multiple login options (email/password, SSO, social)
- Two-factor authentication setup
- Password reset flows
- Account security settings
- Login history and device management

### Technical Requirements
- [ ] Email/password authentication
- [ ] Social login (GitHub, Google, Microsoft)
- [ ] Enterprise SSO (SAML, OIDC)
- [ ] Multi-factor authentication (TOTP, SMS, WebAuthn)
- [ ] Password reset and email verification
- [ ] Session management and JWT tokens
- [ ] Device fingerprinting and trusted devices
- [ ] Account lockout and rate limiting

### Success Criteria
- [ ] <200ms authentication response time
- [ ] 99.9% authentication service uptime
- [ ] Support for 10M+ concurrent sessions
- [ ] Zero password storage vulnerabilities
- [ ] Complete audit logging

## All Needed Context

### Documentation & References
- url: https://firebase.google.com/docs/auth
  why: Firebase Auth implementation patterns
- url: https://auth0.com/docs/secure/attack-protection
  why: Authentication security best practices
- url: https://www.rfc-editor.org/rfc/rfc6238.html
  why: TOTP specification for 2FA
- file: docs/security/README.md
  why: Current security requirements

### Authentication Methods

```yaml
Supported Methods:
  primary:
    - email_password
    - google_oauth
    - github_oauth
    - microsoft_oauth
    
  enterprise:
    - saml_sso
    - oidc_sso
    - ldap_directory
    
  mfa:
    - totp_authenticator
    - sms_verification
    - webauthn_fido2
    - backup_codes
```

### OAuth 2.0 Flow

CCL supports multiple OAuth 2.0 flows for different application types:

#### Authorization Code Flow (Web Applications)
1.  **Redirect to Authorize**: Redirect the user to the CCL authorization endpoint.
    ```
    https://auth.ccl.dev/oauth/authorize?
      client_id=YOUR_CLIENT_ID&
      redirect_uri=YOUR_REDIRECT_URI&
      response_type=code&
      scope=read:analysis write:analysis&
      state=RANDOM_STATE&
      code_challenge=CODE_CHALLENGE&
      code_challenge_method=S256
    ```
2.  **User Authorization**: The user authorizes the application.
3.  **Redirect to Application**: The user is redirected back with an authorization code.
    ```
    YOUR_REDIRECT_URI?code=AUTH_CODE&state=RANDOM_STATE
    ```
4.  **Exchange Code for Token**: Exchange the authorization code for tokens.
    ```http
    POST https://auth.ccl.dev/oauth/token
    Content-Type: application/json

    {
      "grant_type": "authorization_code",
      "code": "AUTH_CODE",
      "client_id": "YOUR_CLIENT_ID",
      "client_secret": "YOUR_CLIENT_SECRET",
      "code_verifier": "CODE_VERIFIER"
    }
    ```
5.  **Receive Tokens**: Receive the access token and refresh token.
    ```json
    {
      "access_token": "ACCESS_TOKEN",
      "token_type": "Bearer",
      "expires_in": 3600,
      "refresh_token": "REFRESH_TOKEN",
      "scope": "read:analysis write:analysis",
      "id_token": "ID_TOKEN"
    }
    ```

#### Client Credentials Flow (Server-to-Server)
```http
POST https://auth.ccl.dev/oauth/token
Content-Type: application/x-www-form-urlencoded

grant_type=client_credentials&
client_id=YOUR_CLIENT_ID&
client_secret=YOUR_CLIENT_SECRET&
scope=read:analysis
```

#### Implicit Flow (Deprecated - Use Authorization Code + PKCE)
For security reasons, implicit flow is deprecated. Use Authorization Code with PKCE instead.

#### Device Authorization Flow (CLI/IoT)
```http
POST https://auth.ccl.dev/oauth/device/authorize
Content-Type: application/x-www-form-urlencoded

client_id=YOUR_CLIENT_ID&scope=read:analysis
```

### SAML 2.0 SSO Integration

CCL supports SAML 2.0 for enterprise single sign-on:

#### SAML Configuration
```xml
<!-- Service Provider Metadata -->
<EntityDescriptor entityID="https://api.ccl.dev">
  <SPSSODescriptor>
    <NameIDFormat>urn:oasis:names:tc:SAML:2.0:nameid-format:emailAddress</NameIDFormat>
    <AssertionConsumerService
      Binding="urn:oasis:names:tc:SAML:2.0:bindings:HTTP-POST"
      Location="https://auth.ccl.dev/saml/acs"
      index="0" isDefault="true"/>
  </SPSSODescriptor>
</EntityDescriptor>
```

#### SAML Assertion Requirements
- **NameID**: Must be email address
- **Required Attributes**:
  - `email` (required)
  - `name` or `displayName` (required)
  - `groups` or `roles` (optional)
  - `department` (optional)
- **Signature**: Assertions must be signed
- **Encryption**: Support for encrypted assertions
- **Binding**: HTTP-POST binding supported

### OpenID Connect (OIDC) Integration

CCL is a certified OpenID Connect provider supporting:

#### OIDC Discovery
```json
{
  "issuer": "https://auth.ccl.dev",
  "authorization_endpoint": "https://auth.ccl.dev/oauth/authorize",
  "token_endpoint": "https://auth.ccl.dev/oauth/token",
  "userinfo_endpoint": "https://auth.ccl.dev/oauth/userinfo",
  "jwks_uri": "https://auth.ccl.dev/.well-known/jwks.json",
  "scopes_supported": ["openid", "profile", "email", "read:analysis"],
  "response_types_supported": ["code", "id_token", "code id_token"],
  "grant_types_supported": ["authorization_code", "client_credentials"],
  "subject_types_supported": ["public"],
  "id_token_signing_alg_values_supported": ["RS256", "ES256"],
  "token_endpoint_auth_methods_supported": ["client_secret_basic", "client_secret_post", "private_key_jwt"]
}
```

#### ID Token Claims
```json
{
  "iss": "https://auth.ccl.dev",
  "sub": "user123",
  "aud": "client_id",
  "exp": **********,
  "iat": **********,
  "email": "<EMAIL>",
  "email_verified": true,
  "name": "John Doe",
  "picture": "https://avatar.url",
  "ccl_roles": ["developer", "admin"],
  "ccl_org": "org_123"
}
```

### Service Account Management

Service accounts are used for machine-to-machine authentication with enhanced security features:

#### Service Account Types
- **Standard**: Basic API access with role-based permissions
- **Enterprise**: Enhanced security with HSM-backed keys
- **Federated**: Workload Identity Federation for GCP resources

#### Service Account Authentication
```typescript
// JWT-based authentication
interface ServiceAccountCredentials {
  type: 'service_account';
  project_id: string;
  private_key_id: string;
  private_key: string; // RSA private key
  client_email: string;
  client_id: string;
  auth_uri: string;
  token_uri: string;
}

// Generate JWT assertion
const assertion = jwt.sign({
  iss: credentials.client_email,
  sub: credentials.client_email,
  aud: 'https://auth.ccl.dev/oauth/token',
  exp: Math.floor(Date.now() / 1000) + 3600,
  iat: Math.floor(Date.now() / 1000),
  scope: 'read:analysis write:patterns'
}, credentials.private_key, {
  algorithm: 'RS256',
  keyid: credentials.private_key_id
});
```

#### Key Management
- **Automatic Rotation**: Keys rotate every 90 days
- **HSM Protection**: Enterprise accounts use Cloud HSM
- **Audit Trail**: All key operations logged
- **Revocation**: Immediate key revocation capability

### Hardware Security Module (HSM) Integration

For enterprise customers, CCL integrates with Google Cloud HSM for enhanced key protection:

#### HSM-Backed Authentication
```yaml
HSM Configuration:
  Protection Level: HSM
  Algorithm: RSA-2048, ECDSA P-256
  Key Rotation: Automatic (90 days)
  Geographic Replication: Multi-region
  
Supported Operations:
  - JWT signing for service accounts
  - SAML assertion signing
  - API token encryption
  - Database encryption keys
```

#### HSM Key Lifecycle
1. **Generation**: Keys generated within HSM boundary
2. **Storage**: Keys never leave HSM in plaintext
3. **Usage**: Cryptographic operations performed in HSM
4. **Rotation**: Automated key rotation with zero downtime
5. **Destruction**: Secure key deletion with audit trail

### Zero Trust Architecture

CCL implements zero trust principles throughout the authentication system:

#### Continuous Verification
- **Every Request**: No implicit trust based on network location
- **Device Attestation**: Device certificate validation
- **Risk Assessment**: Behavioral analysis and anomaly detection
- **Dynamic Policies**: Context-aware access decisions

#### Trust Signals
```typescript
interface TrustSignals {
  device: {
    registered: boolean;
    compliant: boolean;
    lastSeen: Date;
    riskScore: number;
  };
  location: {
    country: string;
    vpn: boolean;
    knownLocation: boolean;
    riskScore: number;
  };
  behavior: {
    typicalHours: boolean;
    velocityAnomaly: boolean;
    accessPatterns: 'normal' | 'suspicious' | 'anomalous';
    riskScore: number;
  };
  session: {
    mfaVerified: boolean;
    age: number;
    privilegeLevel: number;
  };
}
```

### Conditional Access Policies

Enterprise customers can configure conditional access based on risk factors:

#### Policy Engine
```yaml
Access Policies:
  High Risk Users:
    conditions:
      - user.role: ["admin", "owner"]
    requirements:
      - mfa: required
      - device: managed
      - location: allowed_countries
      
  Sensitive Operations:
    conditions:
      - action.type: ["delete", "export", "admin"]
    requirements:
      - mfa: fresh (< 5 minutes)
      - approval: manager_approval
      - session: reauthenticate
      
  Geographic Restrictions:
    conditions:
      - location.country: ["US", "CA", "EU"]
    allow: true
    default: block
```

### Compliance & Audit Features

#### SOC2 Type II Compliance
- **Access Controls**: Role-based access with least privilege
- **Logical Access**: Multi-factor authentication required
- **Change Management**: All authentication changes logged
- **Monitoring**: Real-time security monitoring

#### HIPAA Compliance
- **PHI Protection**: Encryption of all personal health information
- **Audit Logs**: Comprehensive access logging
- **Business Associate Agreements**: Available for healthcare customers
- **Risk Assessments**: Regular security assessments

#### GDPR Compliance
- **Data Portability**: User data export capabilities
- **Right to Erasure**: Complete data deletion on request
- **Consent Management**: Granular privacy controls
- **Data Processing**: Lawful basis documentation

### Enterprise Identity Integration

#### Active Directory/LDAP
```typescript
// LDAP configuration
interface LDAPConfig {
  url: string;
  bindDN: string;
  bindCredentials: string;
  searchBase: string;
  searchFilter: string;
  attributes: {
    mail: string;
    displayName: string;
    memberOf: string;
  };
  tls: {
    enabled: boolean;
    rejectUnauthorized: boolean;
    ca?: string[];
  };
}
```

#### Azure AD Integration
```yaml
Azure AD Configuration:
  Tenant: organization.onmicrosoft.com
  Application ID: app-registration-id
  Directory ID: tenant-id
  Supported Flows:
    - Authorization Code
    - On-Behalf-Of
    - Client Credentials
  Claims:
    - email
    - name
    - groups
    - department
```

### Privacy-Preserving Authentication

#### Anonymous Authentication
For privacy-sensitive scenarios, CCL supports anonymous authentication:

```typescript
// Zero-knowledge proof authentication
interface AnonymousCredentials {
  proof: string; // Zero-knowledge proof
  nullifier: string; // Prevents double-spending
  commitment: string; // Privacy-preserving commitment
}
```

#### Differential Privacy
- **Usage Analytics**: Differential privacy for user behavior analysis
- **Rate Limiting**: Privacy-preserving rate limit calculations
- **Audit Logs**: Sanitized logging with k-anonymity

### Known Gotchas & Library Quirks
- **CRITICAL**: Never store passwords in plain text
- **CRITICAL**: Always use constant-time comparison for tokens
- **GOTCHA**: Session fixation attacks with poor token rotation
- **GOTCHA**: Timing attacks on password verification
- **WARNING**: Social login tokens can be revoked externally
- **TIP**: Use bcrypt with cost factor 12+ for passwords
- **TIP**: Implement proper CSRF protection

## Implementation Blueprint

### Core Authentication Service

```typescript
// services/authService.ts
import bcrypt from 'bcrypt';
import speakeasy from 'speakeasy';
import { JWTService } from './jwtService';
import { AuditLogger } from './auditLogger';

export interface AuthenticationRequest {
  email: string;
  password: string;
  mfaCode?: string;
  deviceFingerprint?: string;
  userAgent?: string;
  ipAddress?: string;
}

export interface AuthenticationResult {
  success: boolean;
  user?: User;
  tokens?: {
    accessToken: string;
    refreshToken: string;
    expiresAt: Date;
  };
  requiresMfa?: boolean;
  mfaMethods?: string[];
  error?: AuthenticationError;
}

export class AuthenticationService {
  constructor(
    private userRepository: UserRepository,
    private sessionRepository: SessionRepository,
    private mfaService: MFAService,
    private jwtService: JWTService,
    private auditLogger: AuditLogger,
    private rateLimiter: RateLimiter
  ) {}
  
  async authenticate(request: AuthenticationRequest): Promise<AuthenticationResult> {
    const { email, password, mfaCode, deviceFingerprint, userAgent, ipAddress } = request;
    
    // Rate limiting check
    const rateLimitKey = `auth:${email}:${ipAddress}`;
    if (await this.rateLimiter.isLimited(rateLimitKey)) {
      await this.auditLogger.log({
        event: 'authentication_rate_limited',
        email,
        ipAddress,
        userAgent
      });
      
      throw new AuthenticationError('Too many login attempts', 'RATE_LIMITED');
    }
    
    try {
      // Find user by email
      const user = await this.userRepository.findByEmail(email);
      if (!user) {
        await this.handleFailedAuth(email, ipAddress, 'USER_NOT_FOUND');
        throw new AuthenticationError('Invalid credentials', 'INVALID_CREDENTIALS');
      }
      
      // Check if account is locked
      if (user.lockedUntil && user.lockedUntil > new Date()) {
        await this.auditLogger.log({
          event: 'authentication_account_locked',
          userId: user.id,
          email,
          ipAddress,
          lockedUntil: user.lockedUntil
        });
        
        throw new AuthenticationError('Account temporarily locked', 'ACCOUNT_LOCKED');
      }
      
      // Verify password
      const passwordValid = await this.verifyPassword(password, user.passwordHash);
      if (!passwordValid) {
        await this.handleFailedAuth(email, ipAddress, 'INVALID_PASSWORD', user);
        throw new AuthenticationError('Invalid credentials', 'INVALID_CREDENTIALS');
      }
      
      // Check if MFA is required
      if (user.mfaEnabled && !mfaCode) {
        await this.auditLogger.log({
          event: 'authentication_mfa_required',
          userId: user.id,
          email,
          ipAddress
        });
        
        return {
          success: false,
          requiresMfa: true,
          mfaMethods: await this.mfaService.getEnabledMethods(user.id)
        };
      }
      
      // Verify MFA if provided
      if (user.mfaEnabled && mfaCode) {
        const mfaValid = await this.mfaService.verifyCode(user.id, mfaCode);
        if (!mfaValid) {
          await this.handleFailedAuth(email, ipAddress, 'INVALID_MFA', user);
          throw new AuthenticationError('Invalid MFA code', 'INVALID_MFA');
        }
      }
      
      // Reset failed attempts on successful auth
      if (user.failedLoginAttempts > 0) {
        await this.userRepository.update(user.id, {
          failedLoginAttempts: 0,
          lockedUntil: null
        });
      }
      
      // Generate session and tokens
      const session = await this.createUserSession(user, {
        deviceFingerprint,
        userAgent,
        ipAddress
      });
      
      const tokens = await this.jwtService.generateTokens({
        userId: user.id,
        sessionId: session.id,
        email: user.email,
        roles: user.roles
      });
      
      await this.auditLogger.log({
        event: 'authentication_success',
        userId: user.id,
        email,
        sessionId: session.id,
        ipAddress,
        userAgent,
        mfaUsed: !!mfaCode
      });
      
      return {
        success: true,
        user,
        tokens: {
          accessToken: tokens.accessToken,
          refreshToken: tokens.refreshToken,
          expiresAt: tokens.expiresAt
        }
      };
      
    } catch (error) {
      if (error instanceof AuthenticationError) {
        throw error;
      }
      
      await this.auditLogger.log({
        event: 'authentication_error',
        email,
        ipAddress,
        error: error.message
      });
      
      throw new AuthenticationError('Authentication failed', 'INTERNAL_ERROR');
    }
  }
  
  private async verifyPassword(password: string, hash: string): Promise<boolean> {
    try {
      return await bcrypt.compare(password, hash);
    } catch (error) {
      // Log but don't expose timing information
      return false;
    }
  }
  
  private async handleFailedAuth(
    email: string, 
    ipAddress: string, 
    reason: string, 
    user?: User
  ): Promise<void> {
    // Increment rate limiter
    await this.rateLimiter.increment(`auth:${email}:${ipAddress}`);
    
    if (user) {
      // Increment failed attempts
      const failedAttempts = (user.failedLoginAttempts || 0) + 1;
      const updates: Partial<User> = { failedLoginAttempts: failedAttempts };
      
      // Lock account after 5 failed attempts
      if (failedAttempts >= 5) {
        updates.lockedUntil = new Date(Date.now() + 30 * 60 * 1000); // 30 minutes
      }
      
      await this.userRepository.update(user.id, updates);
    }
    
    await this.auditLogger.log({
      event: 'authentication_failed',
      email,
      ipAddress,
      reason,
      userId: user?.id
    });
  }
  
  private async createUserSession(
    user: User, 
    context: {
      deviceFingerprint?: string;
      userAgent?: string;
      ipAddress?: string;
    }
  ): Promise<UserSession> {
    const session: UserSession = {
      id: crypto.randomUUID(),
      userId: user.id,
      deviceFingerprint: context.deviceFingerprint,
      userAgent: context.userAgent,
      ipAddress: context.ipAddress,
      createdAt: new Date(),
      lastActiveAt: new Date(),
      expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days
      active: true
    };
    
    await this.sessionRepository.save(session);
    
    // Clean up old sessions (keep only 5 most recent)
    await this.sessionRepository.cleanupOldSessions(user.id, 5);
    
    return session;
  }
}
```

### Multi-Factor Authentication Service

```typescript
// services/mfaService.ts
import speakeasy from 'speakeasy';
import QRCode from 'qrcode';

export interface MFASetupResult {
  secret: string;
  qrCodeUrl: string;
  backupCodes: string[];
}

export class MFAService {
  constructor(
    private mfaRepository: MFARepository,
    private userRepository: UserRepository,
    private smsService: SMSService
  ) {}
  
  async setupTOTP(userId: string): Promise<MFASetupResult> {
    const user = await this.userRepository.findById(userId);
    if (!user) {
      throw new Error('User not found');
    }
    
    // Generate TOTP secret
    const secret = speakeasy.generateSecret({
      name: `CCL (${user.email})`,
      issuer: 'CCL'
    });
    
    // Generate QR code
    const qrCodeUrl = await QRCode.toDataURL(secret.otpauth_url!);
    
    // Generate backup codes
    const backupCodes = this.generateBackupCodes(10);
    
    // Store MFA configuration (not yet enabled)
    await this.mfaRepository.storeTOTPSetup(userId, {
      secret: secret.base32,
      backupCodes: await this.hashBackupCodes(backupCodes),
      verified: false
    });
    
    return {
      secret: secret.base32,
      qrCodeUrl,
      backupCodes
    };
  }
  
  async verifyAndEnableTOTP(userId: string, code: string): Promise<boolean> {
    const mfaConfig = await this.mfaRepository.getTOTPConfig(userId);
    if (!mfaConfig || mfaConfig.verified) {
      return false;
    }
    
    const verified = speakeasy.totp.verify({
      secret: mfaConfig.secret,
      encoding: 'base32',
      token: code,
      window: 2 // Allow 2 time steps (±60 seconds)
    });
    
    if (verified) {
      // Enable MFA for user
      await this.mfaRepository.enableTOTP(userId);
      await this.userRepository.update(userId, { mfaEnabled: true });
      
      return true;
    }
    
    return false;
  }
  
  async verifyCode(userId: string, code: string): Promise<boolean> {
    const mfaConfig = await this.mfaRepository.getTOTPConfig(userId);
    if (!mfaConfig || !mfaConfig.verified) {
      return false;
    }
    
    // Try TOTP verification first
    const totpValid = speakeasy.totp.verify({
      secret: mfaConfig.secret,
      encoding: 'base32',
      token: code,
      window: 2
    });
    
    if (totpValid) {
      return true;
    }
    
    // Try backup codes
    const backupCodeValid = await this.verifyBackupCode(userId, code);
    if (backupCodeValid) {
      // Mark backup code as used
      await this.mfaRepository.markBackupCodeUsed(userId, code);
      return true;
    }
    
    return false;
  }
  
  async sendSMSCode(userId: string, phoneNumber: string): Promise<void> {
    const code = this.generateSMSCode();
    const expiresAt = new Date(Date.now() + 5 * 60 * 1000); // 5 minutes
    
    // Store SMS code
    await this.mfaRepository.storeSMSCode(userId, {
      code: await this.hashSMSCode(code),
      phoneNumber,
      expiresAt
    });
    
    // Send SMS
    await this.smsService.send(phoneNumber, `Your CCL verification code is: ${code}`);
  }
  
  async verifySMSCode(userId: string, code: string): Promise<boolean> {
    const smsConfig = await this.mfaRepository.getSMSConfig(userId);
    if (!smsConfig || smsConfig.expiresAt < new Date()) {
      return false;
    }
    
    const codeValid = await bcrypt.compare(code, smsConfig.hashedCode);
    if (codeValid) {
      // Remove used SMS code
      await this.mfaRepository.deleteSMSCode(userId);
      return true;
    }
    
    return false;
  }
  
  async getEnabledMethods(userId: string): Promise<string[]> {
    const methods: string[] = [];
    
    const totpConfig = await this.mfaRepository.getTOTPConfig(userId);
    if (totpConfig?.verified) {
      methods.push('totp');
    }
    
    const smsConfig = await this.mfaRepository.getSMSConfig(userId);
    if (smsConfig) {
      methods.push('sms');
    }
    
    const webauthnCredentials = await this.mfaRepository.getWebAuthnCredentials(userId);
    if (webauthnCredentials.length > 0) {
      methods.push('webauthn');
    }
    
    return methods;
  }
  
  private generateBackupCodes(count: number): string[] {
    const codes: string[] = [];
    for (let i = 0; i < count; i++) {
      const code = crypto.randomBytes(4).toString('hex').toUpperCase();
      codes.push(`${code.substring(0, 4)}-${code.substring(4, 8)}`);
    }
    return codes;
  }
  
  private async hashBackupCodes(codes: string[]): Promise<string[]> {
    return Promise.all(codes.map(code => bcrypt.hash(code, 12)));
  }
  
  private generateSMSCode(): string {
    return Math.floor(100000 + Math.random() * 900000).toString();
  }
  
  private async hashSMSCode(code: string): Promise<string> {
    return bcrypt.hash(code, 12);
  }
  
  private async verifyBackupCode(userId: string, code: string): Promise<boolean> {
    const mfaConfig = await this.mfaRepository.getTOTPConfig(userId);
    if (!mfaConfig?.backupCodes) {
      return false;
    }
    
    for (const hashedCode of mfaConfig.backupCodes) {
      if (await bcrypt.compare(code, hashedCode)) {
        return true;
      }
    }
    
    return false;
  }
}
```

### Social Authentication Provider

```typescript
// services/socialAuthService.ts
export interface SocialAuthConfig {
  google: {
    clientId: string;
    clientSecret: string;
  };
  github: {
    clientId: string;
    clientSecret: string;
  };
  microsoft: {
    clientId: string;
    clientSecret: string;
    tenantId: string;
  };
}

export interface SocialUserInfo {
  id: string;
  email: string;
  name: string;
  avatarUrl?: string;
  provider: string;
}

export class SocialAuthService {
  constructor(
    private config: SocialAuthConfig,
    private userRepository: UserRepository,
    private socialAccountRepository: SocialAccountRepository,
    private authService: AuthenticationService
  ) {}
  
  async handleGoogleAuth(code: string, redirectUri: string): Promise<AuthenticationResult> {
    try {
      // Exchange code for tokens
      const tokenResponse = await fetch('https://oauth2.googleapis.com/token', {
        method: 'POST',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        body: new URLSearchParams({
          client_id: this.config.google.clientId,
          client_secret: this.config.google.clientSecret,
          code,
          grant_type: 'authorization_code',
          redirect_uri: redirectUri
        })
      });
      
      const tokens = await tokenResponse.json();
      
      // Get user info
      const userResponse = await fetch('https://www.googleapis.com/oauth2/v2/userinfo', {
        headers: { Authorization: `Bearer ${tokens.access_token}` }
      });
      
      const googleUser = await userResponse.json();
      
      const socialUser: SocialUserInfo = {
        id: googleUser.id,
        email: googleUser.email,
        name: googleUser.name,
        avatarUrl: googleUser.picture,
        provider: 'google'
      };
      
      return this.handleSocialLogin(socialUser);
      
    } catch (error) {
      throw new AuthenticationError('Google authentication failed', 'SOCIAL_AUTH_ERROR');
    }
  }
  
  async handleGitHubAuth(code: string): Promise<AuthenticationResult> {
    try {
      // Exchange code for access token
      const tokenResponse = await fetch('https://github.com/login/oauth/access_token', {
        method: 'POST',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          client_id: this.config.github.clientId,
          client_secret: this.config.github.clientSecret,
          code
        })
      });
      
      const tokens = await tokenResponse.json();
      
      // Get user info
      const userResponse = await fetch('https://api.github.com/user', {
        headers: { Authorization: `Bearer ${tokens.access_token}` }
      });
      
      const githubUser = await userResponse.json();
      
      // Get primary email
      const emailResponse = await fetch('https://api.github.com/user/emails', {
        headers: { Authorization: `Bearer ${tokens.access_token}` }
      });
      
      const emails = await emailResponse.json();
      const primaryEmail = emails.find((email: any) => email.primary)?.email;
      
      const socialUser: SocialUserInfo = {
        id: githubUser.id.toString(),
        email: primaryEmail || githubUser.email,
        name: githubUser.name || githubUser.login,
        avatarUrl: githubUser.avatar_url,
        provider: 'github'
      };
      
      return this.handleSocialLogin(socialUser);
      
    } catch (error) {
      throw new AuthenticationError('GitHub authentication failed', 'SOCIAL_AUTH_ERROR');
    }
  }
  
  private async handleSocialLogin(socialUser: SocialUserInfo): Promise<AuthenticationResult> {
    // Check if social account exists
    let socialAccount = await this.socialAccountRepository.findByProviderAndId(
      socialUser.provider,
      socialUser.id
    );
    
    let user: User;
    
    if (socialAccount) {
      // Existing social account - get user
      user = await this.userRepository.findById(socialAccount.userId);
      if (!user) {
        throw new AuthenticationError('User account not found', 'USER_NOT_FOUND');
      }
      
      // Update social account info
      await this.socialAccountRepository.update(socialAccount.id, {
        email: socialUser.email,
        name: socialUser.name,
        avatarUrl: socialUser.avatarUrl,
        lastLoginAt: new Date()
      });
      
    } else {
      // Check if user exists with this email
      user = await this.userRepository.findByEmail(socialUser.email);
      
      if (user) {
        // Link social account to existing user
        socialAccount = await this.socialAccountRepository.create({
          userId: user.id,
          provider: socialUser.provider,
          providerId: socialUser.id,
          email: socialUser.email,
          name: socialUser.name,
          avatarUrl: socialUser.avatarUrl,
          createdAt: new Date(),
          lastLoginAt: new Date()
        });
        
      } else {
        // Create new user
        user = await this.userRepository.create({
          email: socialUser.email,
          name: socialUser.name,
          avatarUrl: socialUser.avatarUrl,
          emailVerified: true, // Trust social provider verification
          createdAt: new Date(),
          lastLoginAt: new Date(),
          authProvider: socialUser.provider
        });
        
        // Create social account link
        socialAccount = await this.socialAccountRepository.create({
          userId: user.id,
          provider: socialUser.provider,
          providerId: socialUser.id,
          email: socialUser.email,
          name: socialUser.name,
          avatarUrl: socialUser.avatarUrl,
          createdAt: new Date(),
          lastLoginAt: new Date()
        });
      }
    }
    
    // Create session and generate tokens
    const session = await this.authService.createUserSession(user, {});
    const tokens = await this.authService.generateTokens({
      userId: user.id,
      sessionId: session.id,
      email: user.email,
      roles: user.roles
    });
    
    return {
      success: true,
      user,
      tokens: {
        accessToken: tokens.accessToken,
        refreshToken: tokens.refreshToken,
        expiresAt: tokens.expiresAt
      }
    };
  }
}
```

### WebAuthn/FIDO2 Authentication Service

```typescript
// services/webauthnService.ts
import {
  generateRegistrationOptions,
  verifyRegistrationResponse,
  generateAuthenticationOptions,
  verifyAuthenticationResponse,
} from '@simplewebauthn/server';
import type {
  GenerateRegistrationOptionsOpts,
  GenerateAuthenticationOptionsOpts,
  VerifyRegistrationResponseOpts,
  VerifyAuthenticationResponseOpts,
} from '@simplewebauthn/server';

export interface WebAuthnCredential {
  id: string;
  userId: string;
  credentialId: string;
  publicKey: Buffer;
  counter: number;
  deviceType: 'singleDevice' | 'multiDevice';
  backedUp: boolean;
  transports?: ('ble' | 'hybrid' | 'internal' | 'nfc' | 'usb')[];
  createdAt: Date;
  lastUsedAt: Date;
  friendlyName?: string;
}

export class WebAuthnService {
  private readonly rpName = 'CCL Platform';
  private readonly rpId = process.env.WEBAUTHN_RP_ID || 'localhost';
  private readonly origin = process.env.WEBAUTHN_ORIGIN || 'http://localhost:3000';

  constructor(
    private credentialRepository: WebAuthnCredentialRepository,
    private userRepository: UserRepository,
    private challengeRepository: ChallengeRepository
  ) {}

  async generateRegistrationOptions(userId: string): Promise<any> {
    const user = await this.userRepository.findById(userId);
    if (!user) {
      throw new Error('User not found');
    }

    // Get existing credentials to exclude
    const existingCredentials = await this.credentialRepository.findByUserId(userId);
    const excludeCredentials = existingCredentials.map(cred => ({
      id: Buffer.from(cred.credentialId, 'base64url'),
      type: 'public-key' as const,
    }));

    const options = await generateRegistrationOptions({
      rpName: this.rpName,
      rpID: this.rpId,
      userID: user.id,
      userName: user.email,
      userDisplayName: user.name || user.email,
      timeout: 60000,
      attestationType: 'none',
      excludeCredentials,
      authenticatorSelection: {
        residentKey: 'preferred',
        userVerification: 'preferred',
        authenticatorAttachment: 'cross-platform',
      },
      supportedAlgorithmIDs: [-7, -257], // ES256, RS256
    });

    // Store challenge for verification
    await this.challengeRepository.store(userId, {
      challenge: options.challenge,
      type: 'registration',
      expiresAt: new Date(Date.now() + 5 * 60 * 1000), // 5 minutes
    });

    return options;
  }

  async verifyRegistration(
    userId: string,
    registrationResponse: any,
    friendlyName?: string
  ): Promise<WebAuthnCredential> {
    const challenge = await this.challengeRepository.get(userId, 'registration');
    if (!challenge || challenge.expiresAt < new Date()) {
      throw new Error('Invalid or expired challenge');
    }

    const verification = await verifyRegistrationResponse({
      response: registrationResponse,
      expectedChallenge: challenge.challenge,
      expectedOrigin: this.origin,
      expectedRPID: this.rpId,
      requireUserVerification: false,
    });

    if (!verification.verified || !verification.registrationInfo) {
      throw new Error('Registration verification failed');
    }

    const { credentialID, credentialPublicKey, counter } = verification.registrationInfo;

    // Check if credential already exists
    const existingCredential = await this.credentialRepository.findByCredentialId(
      Buffer.from(credentialID).toString('base64url')
    );

    if (existingCredential) {
      throw new Error('Credential already registered');
    }

    // Create new credential
    const credential: WebAuthnCredential = {
      id: crypto.randomUUID(),
      userId,
      credentialId: Buffer.from(credentialID).toString('base64url'),
      publicKey: Buffer.from(credentialPublicKey),
      counter,
      deviceType: verification.registrationInfo.credentialDeviceType,
      backedUp: verification.registrationInfo.credentialBackedUp,
      transports: registrationResponse.response.transports,
      createdAt: new Date(),
      lastUsedAt: new Date(),
      friendlyName: friendlyName || `Security Key ${Date.now()}`,
    };

    await this.credentialRepository.save(credential);
    await this.challengeRepository.delete(userId, 'registration');

    return credential;
  }

  async generateAuthenticationOptions(userId?: string): Promise<any> {
    let allowCredentials: any[] = [];

    if (userId) {
      // Get user's credentials
      const userCredentials = await this.credentialRepository.findByUserId(userId);
      allowCredentials = userCredentials.map(cred => ({
        id: Buffer.from(cred.credentialId, 'base64url'),
        type: 'public-key' as const,
        transports: cred.transports,
      }));
    }

    const options = await generateAuthenticationOptions({
      timeout: 60000,
      allowCredentials: allowCredentials.length > 0 ? allowCredentials : undefined,
      userVerification: 'preferred',
      rpID: this.rpId,
    });

    // Store challenge
    const challengeKey = userId || 'anonymous';
    await this.challengeRepository.store(challengeKey, {
      challenge: options.challenge,
      type: 'authentication',
      expiresAt: new Date(Date.now() + 5 * 60 * 1000),
    });

    return options;
  }

  async verifyAuthentication(
    authenticationResponse: any,
    userId?: string
  ): Promise<{ verified: boolean; user?: User; credential?: WebAuthnCredential }> {
    const challengeKey = userId || 'anonymous';
    const challenge = await this.challengeRepository.get(challengeKey, 'authentication');
    
    if (!challenge || challenge.expiresAt < new Date()) {
      throw new Error('Invalid or expired challenge');
    }

    // Find credential by ID
    const credentialId = Buffer.from(authenticationResponse.id, 'base64url').toString('base64url');
    const credential = await this.credentialRepository.findByCredentialId(credentialId);

    if (!credential) {
      throw new Error('Credential not found');
    }

    // Get user
    const user = await this.userRepository.findById(credential.userId);
    if (!user) {
      throw new Error('User not found');
    }

    const verification = await verifyAuthenticationResponse({
      response: authenticationResponse,
      expectedChallenge: challenge.challenge,
      expectedOrigin: this.origin,
      expectedRPID: this.rpId,
      authenticator: {
        credentialID: Buffer.from(credential.credentialId, 'base64url'),
        credentialPublicKey: credential.publicKey,
        counter: credential.counter,
        transports: credential.transports,
      },
      requireUserVerification: false,
    });

    if (verification.verified) {
      // Update credential counter and last used
      await this.credentialRepository.update(credential.id, {
        counter: verification.authenticationInfo.newCounter,
        lastUsedAt: new Date(),
      });

      await this.challengeRepository.delete(challengeKey, 'authentication');

      return { verified: true, user, credential };
    }

    return { verified: false };
  }

  async getUserCredentials(userId: string): Promise<WebAuthnCredential[]> {
    return this.credentialRepository.findByUserId(userId);
  }

  async deleteCredential(userId: string, credentialId: string): Promise<void> {
    const credential = await this.credentialRepository.findByCredentialId(credentialId);
    
    if (!credential || credential.userId !== userId) {
      throw new Error('Credential not found or access denied');
    }

    await this.credentialRepository.delete(credentialId);
  }

  async updateCredentialName(
    userId: string,
    credentialId: string,
    friendlyName: string
  ): Promise<void> {
    const credential = await this.credentialRepository.findByCredentialId(credentialId);
    
    if (!credential || credential.userId !== userId) {
      throw new Error('Credential not found or access denied');
    }

    await this.credentialRepository.update(credential.id, { friendlyName });
  }
}
```

### Enterprise SSO Service

```typescript
// services/ssoService.ts
import { Strategy as SAMLStrategy } from 'passport-saml';
import { Strategy as OIDCStrategy } from 'passport-openidconnect';

export interface SSOConfig {
  saml?: {
    entryPoint: string;
    issuer: string;
    cert: string;
    wantAssertionsSigned: boolean;
    wantNameIdEncrypted: boolean;
    signatureAlgorithm: string;
  };
  oidc?: {
    issuer: string;
    clientID: string;
    clientSecret: string;
    scope: string[];
    responseType: string;
  };
}

export interface SSOUserInfo {
  id: string;
  email: string;
  name: string;
  roles?: string[];
  department?: string;
  provider: 'saml' | 'oidc';
}

export class SSOService {
  constructor(
    private userRepository: UserRepository,
    private ssoConfigRepository: SSOConfigRepository,
    private authService: AuthenticationService
  ) {}

  async configureSAML(organizationId: string, config: SSOConfig['saml']): Promise<void> {
    if (!config) throw new Error('SAML configuration required');

    // Validate SAML configuration
    this.validateSAMLConfig(config);

    await this.ssoConfigRepository.save(organizationId, {
      type: 'saml',
      config,
      enabled: true,
      createdAt: new Date(),
    });
  }

  async configureOIDC(organizationId: string, config: SSOConfig['oidc']): Promise<void> {
    if (!config) throw new Error('OIDC configuration required');

    // Validate OIDC configuration
    this.validateOIDCConfig(config);

    await this.ssoConfigRepository.save(organizationId, {
      type: 'oidc',
      config,
      enabled: true,
      createdAt: new Date(),
    });
  }

  async handleSAMLResponse(
    organizationId: string,
    samlResponse: any
  ): Promise<AuthenticationResult> {
    const ssoConfig = await this.ssoConfigRepository.findByOrganization(organizationId);
    
    if (!ssoConfig || ssoConfig.type !== 'saml' || !ssoConfig.enabled) {
      throw new Error('SAML not configured for organization');
    }

    // Process SAML assertion
    const userInfo = this.processSAMLAssertion(samlResponse);
    
    return this.handleSSOLogin(userInfo, organizationId);
  }

  async handleOIDCCallback(
    organizationId: string,
    oidcUserInfo: any
  ): Promise<AuthenticationResult> {
    const ssoConfig = await this.ssoConfigRepository.findByOrganization(organizationId);
    
    if (!ssoConfig || ssoConfig.type !== 'oidc' || !ssoConfig.enabled) {
      throw new Error('OIDC not configured for organization');
    }

    const userInfo: SSOUserInfo = {
      id: oidcUserInfo.sub,
      email: oidcUserInfo.email,
      name: oidcUserInfo.name || oidcUserInfo.preferred_username,
      roles: oidcUserInfo.roles || [],
      department: oidcUserInfo.department,
      provider: 'oidc',
    };

    return this.handleSSOLogin(userInfo, organizationId);
  }

  private async handleSSOLogin(
    ssoUser: SSOUserInfo,
    organizationId: string
  ): Promise<AuthenticationResult> {
    // Check if user exists
    let user = await this.userRepository.findByEmail(ssoUser.email);

    if (user) {
      // Update user info from SSO
      await this.userRepository.update(user.id, {
        name: ssoUser.name,
        lastLoginAt: new Date(),
        ssoProvider: ssoUser.provider,
        organizationId,
      });
    } else {
      // Create new user from SSO
      user = await this.userRepository.create({
        email: ssoUser.email,
        name: ssoUser.name,
        emailVerified: true,
        ssoProvider: ssoUser.provider,
        organizationId,
        roles: this.mapSSORole(ssoUser.roles),
        createdAt: new Date(),
        lastLoginAt: new Date(),
      });
    }

    // Create session and tokens
    const session = await this.authService.createUserSession(user, {});
    const tokens = await this.authService.generateTokens({
      userId: user.id,
      sessionId: session.id,
      email: user.email,
      roles: user.roles,
    });

    return {
      success: true,
      user,
      tokens: {
        accessToken: tokens.accessToken,
        refreshToken: tokens.refreshToken,
        expiresAt: tokens.expiresAt,
      },
    };
  }

  private validateSAMLConfig(config: SSOConfig['saml']): void {
    if (!config?.entryPoint) throw new Error('SAML entry point required');
    if (!config?.issuer) throw new Error('SAML issuer required');
    if (!config?.cert) throw new Error('SAML certificate required');
  }

  private validateOIDCConfig(config: SSOConfig['oidc']): void {
    if (!config?.issuer) throw new Error('OIDC issuer required');
    if (!config?.clientID) throw new Error('OIDC client ID required');
    if (!config?.clientSecret) throw new Error('OIDC client secret required');
  }

  private processSAMLAssertion(response: any): SSOUserInfo {
    // Extract user information from SAML assertion
    return {
      id: response.nameID,
      email: response.attributes?.email || response.nameID,
      name: response.attributes?.name || response.attributes?.displayName,
      roles: response.attributes?.roles || [],
      department: response.attributes?.department,
      provider: 'saml',
    };
  }

  private mapSSORole(ssoRoles?: string[]): string[] {
    const roleMapping: Record<string, string> = {
      'admin': 'admin',
      'administrator': 'admin',
      'user': 'user',
      'developer': 'developer',
      'viewer': 'viewer',
    };

    return ssoRoles?.map(role => roleMapping[role.toLowerCase()] || 'user') || ['user'];
  }
}
```

### Password Management

```typescript
// services/passwordService.ts
export class PasswordService {
  private readonly SALT_ROUNDS = 12;
  private readonly MIN_PASSWORD_LENGTH = 8;
  private readonly PASSWORD_HISTORY_COUNT = 5;
  
  constructor(
    private userRepository: UserRepository,
    private passwordHistoryRepository: PasswordHistoryRepository,
    private emailService: EmailService
  ) {}
  
  async hashPassword(password: string): Promise<string> {
    return bcrypt.hash(password, this.SALT_ROUNDS);
  }
  
  async validatePasswordStrength(password: string): Promise<{
    valid: boolean;
    errors: string[];
    score: number;
  }> {
    const errors: string[] = [];
    let score = 0;
    
    // Length check
    if (password.length < this.MIN_PASSWORD_LENGTH) {
      errors.push(`Password must be at least ${this.MIN_PASSWORD_LENGTH} characters long`);
    } else {
      score += 1;
    }
    
    // Character variety checks
    if (!/[a-z]/.test(password)) {
      errors.push('Password must contain at least one lowercase letter');
    } else {
      score += 1;
    }
    
    if (!/[A-Z]/.test(password)) {
      errors.push('Password must contain at least one uppercase letter');
    } else {
      score += 1;
    }
    
    if (!/\d/.test(password)) {
      errors.push('Password must contain at least one number');
    } else {
      score += 1;
    }
    
    if (!/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
      errors.push('Password must contain at least one special character');
    } else {
      score += 1;
    }
    
    // Common password check
    if (this.isCommonPassword(password)) {
      errors.push('Password is too common');
    } else {
      score += 1;
    }
    
    return {
      valid: errors.length === 0,
      errors,
      score: Math.min(score, 5) // Max score of 5
    };
  }
  
  async changePassword(
    userId: string,
    currentPassword: string,
    newPassword: string
  ): Promise<void> {
    const user = await this.userRepository.findById(userId);
    if (!user) {
      throw new Error('User not found');
    }
    
    // Verify current password (if user has one)
    if (user.passwordHash) {
      const currentValid = await bcrypt.compare(currentPassword, user.passwordHash);
      if (!currentValid) {
        throw new AuthenticationError('Current password is incorrect', 'INVALID_PASSWORD');
      }
    }
    
    // Validate new password strength
    const validation = await this.validatePasswordStrength(newPassword);
    if (!validation.valid) {
      throw new ValidationError('Password does not meet requirements', validation.errors);
    }
    
    // Check password history
    const passwordHistory = await this.passwordHistoryRepository.getHistory(
      userId,
      this.PASSWORD_HISTORY_COUNT
    );
    
    for (const historicalHash of passwordHistory) {
      if (await bcrypt.compare(newPassword, historicalHash.passwordHash)) {
        throw new ValidationError(
          'New password cannot be the same as any of your last 5 passwords'
        );
      }
    }
    
    // Hash new password
    const newPasswordHash = await this.hashPassword(newPassword);
    
    // Update user password
    await this.userRepository.update(userId, {
      passwordHash: newPasswordHash,
      passwordChangedAt: new Date()
    });
    
    // Store in password history
    if (user.passwordHash) {
      await this.passwordHistoryRepository.add(userId, user.passwordHash);
    }
    
    // Invalidate all existing sessions except current one
    await this.sessionRepository.invalidateAllExceptCurrent(userId);
    
    // Send notification email
    await this.emailService.sendPasswordChangedNotification(user.email);
  }
  
  async requestPasswordReset(email: string): Promise<void> {
    const user = await this.userRepository.findByEmail(email);
    if (!user) {
      // Don't reveal whether email exists
      return;
    }
    
    // Generate reset token
    const resetToken = crypto.randomBytes(32).toString('hex');
    const resetTokenHash = await bcrypt.hash(resetToken, 10);
    const expiresAt = new Date(Date.now() + 60 * 60 * 1000); // 1 hour
    
    // Store reset token
    await this.userRepository.update(user.id, {
      passwordResetToken: resetTokenHash,
      passwordResetExpiresAt: expiresAt
    });
    
    // Send reset email
    const resetUrl = `${process.env.FRONTEND_URL}/reset-password?token=${resetToken}&email=${encodeURIComponent(email)}`;
    await this.emailService.sendPasswordResetEmail(email, resetUrl);
  }
  
  async resetPassword(email: string, token: string, newPassword: string): Promise<void> {
    const user = await this.userRepository.findByEmail(email);
    if (!user || !user.passwordResetToken || !user.passwordResetExpiresAt) {
      throw new AuthenticationError('Invalid or expired reset token', 'INVALID_TOKEN');
    }
    
    // Check if token is expired
    if (user.passwordResetExpiresAt < new Date()) {
      throw new AuthenticationError('Reset token has expired', 'TOKEN_EXPIRED');
    }
    
    // Verify reset token
    const tokenValid = await bcrypt.compare(token, user.passwordResetToken);
    if (!tokenValid) {
      throw new AuthenticationError('Invalid reset token', 'INVALID_TOKEN');
    }
    
    // Validate new password
    const validation = await this.validatePasswordStrength(newPassword);
    if (!validation.valid) {
      throw new ValidationError('Password does not meet requirements', validation.errors);
    }
    
    // Hash new password
    const newPasswordHash = await this.hashPassword(newPassword);
    
    // Update user
    await this.userRepository.update(user.id, {
      passwordHash: newPasswordHash,
      passwordResetToken: null,
      passwordResetExpiresAt: null,
      passwordChangedAt: new Date()
    });
    
    // Invalidate all sessions
    await this.sessionRepository.invalidateAll(userId);
    
    // Send confirmation email
    await this.emailService.sendPasswordResetConfirmation(email);
  }
  
  private isCommonPassword(password: string): boolean {
    const commonPasswords = [
      'password', '123456', '123456789', 'qwerty', 'abc123',
      'password123', 'admin', 'letmein', 'welcome', '12345678'
    ];
    
    return commonPasswords.some(common => 
      password.toLowerCase().includes(common.toLowerCase())
    );
  }
}
```

## Validation Loop

### Level 1: Authentication Flow Testing
```typescript
// Test authentication flows
describe('Authentication Service', () => {
  test('successful email/password authentication', async () => {
    const result = await authService.authenticate({
      email: '<EMAIL>',
      password: 'SecurePass123!',
      ipAddress: '***********'
    });
    
    expect(result.success).toBe(true);
    expect(result.user).toBeDefined();
    expect(result.tokens).toBeDefined();
  });
  
  test('failed authentication increments attempts', async () => {
    await authService.authenticate({
      email: '<EMAIL>',
      password: 'wrongpassword',
      ipAddress: '***********'
    }).catch(() => {});
    
    const user = await userRepository.findByEmail('<EMAIL>');
    expect(user.failedLoginAttempts).toBe(1);
  });
  
  test('account locks after 5 failed attempts', async () => {
    for (let i = 0; i < 5; i++) {
      await authService.authenticate({
        email: '<EMAIL>',
        password: 'wrongpassword',
        ipAddress: '***********'
      }).catch(() => {});
    }
    
    const user = await userRepository.findByEmail('<EMAIL>');
    expect(user.lockedUntil).toBeInstanceOf(Date);
  });
});
```

### Level 2: MFA Testing
```typescript
// Test multi-factor authentication
describe('MFA Service', () => {
  test('TOTP setup and verification', async () => {
    const setup = await mfaService.setupTOTP('user123');
    expect(setup.secret).toBeDefined();
    expect(setup.qrCodeUrl).toMatch(/^data:image\/png;base64/);
    expect(setup.backupCodes).toHaveLength(10);
    
    // Generate TOTP code
    const code = speakeasy.totp({
      secret: setup.secret,
      encoding: 'base32'
    });
    
    const verified = await mfaService.verifyAndEnableTOTP('user123', code);
    expect(verified).toBe(true);
  });
  
  test('backup codes work correctly', async () => {
    const setup = await mfaService.setupTOTP('user123');
    await mfaService.verifyAndEnableTOTP('user123', '123456'); // Mock TOTP
    
    // Use backup code
    const backupCodeValid = await mfaService.verifyCode('user123', setup.backupCodes[0]);
    expect(backupCodeValid).toBe(true);
    
    // Same backup code should not work twice
    const secondUse = await mfaService.verifyCode('user123', setup.backupCodes[0]);
    expect(secondUse).toBe(false);
  });
});
```

### Level 3: Security Testing
```typescript
// Test security measures
describe('Authentication Security', () => {
  test('rate limiting prevents brute force', async () => {
    const requests = Array.from({ length: 6 }, () =>
      authService.authenticate({
        email: '<EMAIL>',
        password: 'wrongpassword',
        ipAddress: '***********'
      }).catch(() => {})
    );
    
    await Promise.all(requests);
    
    await expect(
      authService.authenticate({
        email: '<EMAIL>',
        password: 'correctpassword',
        ipAddress: '***********'
      })
    ).rejects.toThrow('Too many login attempts');
  });
  
  test('password hashing uses secure algorithm', async () => {
    const password = 'TestPassword123!';
    const hash = await passwordService.hashPassword(password);
    
    expect(hash).toMatch(/^\$2[aby]\$/); // bcrypt format
    expect(hash.length).toBeGreaterThan(50);
    
    const valid = await bcrypt.compare(password, hash);
    expect(valid).toBe(true);
  });
});
```

## Final Validation Checklist

- [ ] Email/password authentication working
- [ ] Social login providers integrated
- [ ] Multi-factor authentication implemented
- [ ] Password strength validation enforced
- [ ] Account lockout mechanisms active
- [ ] Rate limiting protecting endpoints
- [ ] Session management secure
- [ ] Password reset flow functional
- [ ] Audit logging comprehensive
- [ ] Security testing passed

## Anti-Patterns to Avoid

1. **DON'T store passwords in plain text** - Always hash with bcrypt
2. **DON'T use MD5 or SHA1 for passwords** - Use bcrypt or Argon2
3. **DON'T reveal user existence** - Use constant-time responses
4. **DON'T skip rate limiting** - Prevents brute force attacks
5. **DON'T use weak JWT secrets** - Use cryptographically strong keys
6. **DON'T trust client-side validation** - Always validate server-side
7. **DON'T log sensitive data** - Passwords and tokens are sensitive
8. **DON'T allow unlimited login attempts** - Implement account lockout
9. **DON'T skip MFA for privileged accounts** - Require for admin users
10. **DON'T ignore session security** - Implement proper session management
