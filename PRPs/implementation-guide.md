# CCL Implementation Guide for Claude Code

## Overview

This guide provides step-by-step implementation instructions for developing features in the CCL platform. Follow these patterns to ensure consistency and maintainability.

## Development Environment Setup

### Prerequisites
```bash
# Required tools
- Go 1.21+
- Rust 1.75+
- Python 3.11+
- Node.js 20+
- gcloud CLI
- Docker Desktop
- Terraform 1.5+
```

### Initial Setup
```bash
# Clone repository
git clone https://github.com/your-org/ccl.git
cd ccl

# Install dependencies
make install-deps

# Configure gcloud
gcloud auth login
gcloud config set project ccl-production

# Set up local secrets
./scripts/setup-local-secrets.sh

# Start local development environment
make dev-up
```

## Service Implementation Patterns

### 1. Analysis Engine (Rust)

#### Creating a New Analyzer
```rust
// src/analyzers/mod.rs
pub mod my_analyzer;

// src/analyzers/my_analyzer.rs
use crate::ast::{AstNode, Visitor};
use crate::errors::AnalysisError;

pub struct MyAnalyzer {
    // State fields
}

impl MyAnalyzer {
    pub fn new() -> Self {
        Self {
            // Initialize
        }
    }

    pub async fn analyze(&self, ast: &AstNode) -> Result<AnalysisResult, AnalysisError> {
        // Implementation
        let visitor = MyVisitor::new();
        ast.accept(&visitor)?;
        
        Ok(visitor.into_result())
    }
}

// Implement AST visitor pattern
struct MyVisitor {
    // Visitor state
}

impl Visitor for MyVisitor {
    fn visit_function(&mut self, func: &FunctionNode) -> Result<(), AnalysisError> {
        // Process function
        Ok(())
    }
    
    // Other visit methods...
}
```

#### Testing Analyzers
```rust
#[cfg(test)]
mod tests {
    use super::*;
    use crate::test_utils::{parse_code, create_test_ast};

    #[tokio::test]
    async fn test_analyze_function() {
        let ast = create_test_ast("
            fn example() {
                println!(\"test\");
            }
        ");
        
        let analyzer = MyAnalyzer::new();
        let result = analyzer.analyze(&ast).await.unwrap();
        
        assert_eq!(result.functions.len(), 1);
    }
}
```

### 2. Query Intelligence (Python)

#### Implementing Query Handlers
```python
# query_intelligence/handlers/my_handler.py
from typing import List, Optional
from dataclasses import dataclass
from query_intelligence.base import QueryHandler, QueryContext
from query_intelligence.models import QueryResult, CodeReference

@dataclass
class MyQueryHandler(QueryHandler):
    """Handles specific query patterns."""
    
    async def can_handle(self, query: str, context: QueryContext) -> bool:
        """Determine if this handler can process the query."""
        # Pattern matching logic
        return "my pattern" in query.lower()
    
    async def handle(self, query: str, context: QueryContext) -> QueryResult:
        """Process the query and return results."""
        # Query Vertex AI for understanding
        understanding = await self._understand_query(query)
        
        # Search codebase
        references = await self._search_codebase(
            understanding.search_terms,
            context.repository_id
        )
        
        # Generate response
        response = await self._generate_response(
            query, understanding, references
        )
        
        return QueryResult(
            answer=response.text,
            confidence=response.confidence,
            references=references,
            reasoning=understanding.reasoning
        )
    
    async def _understand_query(self, query: str) -> QueryUnderstanding:
        """Use Gemini to understand query intent."""
        prompt = self._build_understanding_prompt(query)
        response = await self.vertex_client.generate(prompt)
        return QueryUnderstanding.from_response(response)
```

#### Testing Query Handlers
```python
# tests/handlers/test_my_handler.py
import pytest
from unittest.mock import AsyncMock, patch
from query_intelligence.handlers.my_handler import MyQueryHandler

@pytest.mark.asyncio
async def test_can_handle():
    handler = MyQueryHandler()
    context = create_test_context()
    
    assert await handler.can_handle("my pattern query", context)
    assert not await handler.can_handle("other query", context)

@pytest.mark.asyncio
async def test_handle_query():
    handler = MyQueryHandler()
    context = create_test_context()
    
    with patch.object(handler, '_understand_query') as mock_understand:
        mock_understand.return_value = create_test_understanding()
        
        result = await handler.handle("test query", context)
        
        assert result.confidence > 0.8
        assert len(result.references) > 0
```

### 3. Pattern Mining (Python)

#### Creating Pattern Detectors
```python
# pattern_mining/detectors/my_pattern.py
from pattern_mining.base import PatternDetector, Pattern
from pattern_mining.ast import AstAnalyzer
import numpy as np

class MyPatternDetector(PatternDetector):
    """Detects specific code patterns."""
    
    def __init__(self, threshold: float = 0.8):
        self.threshold = threshold
        self.analyzer = AstAnalyzer()
    
    async def detect(self, codebase_id: str) -> List[Pattern]:
        """Detect patterns in the codebase."""
        # Load AST data
        ast_data = await self._load_ast_data(codebase_id)
        
        # Extract features
        features = self._extract_features(ast_data)
        
        # Run pattern detection algorithm
        patterns = await self._detect_patterns(features)
        
        # Filter by confidence
        return [p for p in patterns if p.confidence >= self.threshold]
    
    def _extract_features(self, ast_data: List[AstNode]) -> np.ndarray:
        """Extract numerical features from AST."""
        features = []
        for node in ast_data:
            feature_vector = self.analyzer.node_to_vector(node)
            features.append(feature_vector)
        return np.array(features)
    
    async def _detect_patterns(self, features: np.ndarray) -> List[Pattern]:
        """Apply ML algorithm to detect patterns."""
        # Use clustering or other ML techniques
        from sklearn.cluster import DBSCAN
        
        clustering = DBSCAN(eps=0.3, min_samples=5)
        labels = clustering.fit_predict(features)
        
        # Convert clusters to patterns
        patterns = []
        for label in np.unique(labels):
            if label != -1:  # Skip noise
                pattern = self._cluster_to_pattern(features[labels == label])
                patterns.append(pattern)
        
        return patterns
```

### 4. Marketplace Service (Go)

#### Implementing Pattern APIs
```go
// marketplace/handlers/pattern_handler.go
package handlers

import (
    "context"
    "net/http"
    
    "github.com/gin-gonic/gin"
    "github.com/your-org/ccl/marketplace/models"
    "github.com/your-org/ccl/marketplace/services"
)

type PatternHandler struct {
    service *services.PatternService
}

func NewPatternHandler(service *services.PatternService) *PatternHandler {
    return &PatternHandler{service: service}
}

// CreatePattern handles pattern creation
func (h *PatternHandler) CreatePattern(c *gin.Context) {
    var req models.CreatePatternRequest
    if err := c.ShouldBindJSON(&req); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
        return
    }
    
    // Validate request
    if err := req.Validate(); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
        return
    }
    
    // Get user from context
    userID := c.GetString("user_id")
    
    // Create pattern
    pattern, err := h.service.CreatePattern(c.Request.Context(), userID, &req)
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create pattern"})
        return
    }
    
    c.JSON(http.StatusCreated, pattern)
}

// Service implementation
package services

type PatternService struct {
    repo     repositories.PatternRepository
    storage  storage.Client
    pubsub   pubsub.Client
}

func (s *PatternService) CreatePattern(ctx context.Context, userID string, req *models.CreatePatternRequest) (*models.Pattern, error) {
    // Begin transaction
    tx, err := s.repo.BeginTx(ctx)
    if err != nil {
        return nil, err
    }
    defer tx.Rollback()
    
    // Create pattern record
    pattern := &models.Pattern{
        ID:          generateID(),
        UserID:      userID,
        Name:        req.Name,
        Description: req.Description,
        Price:       req.Price,
        Status:      models.PatternStatusPending,
    }
    
    if err := tx.CreatePattern(ctx, pattern); err != nil {
        return nil, err
    }
    
    // Store pattern artifact
    artifactPath := fmt.Sprintf("patterns/%s/artifact.wasm", pattern.ID)
    if err := s.storage.Upload(ctx, artifactPath, req.ArtifactData); err != nil {
        return nil, err
    }
    
    // Publish event
    event := &events.PatternCreated{
        PatternID: pattern.ID,
        UserID:    userID,
        Timestamp: time.Now(),
    }
    
    if err := s.pubsub.Publish(ctx, "pattern-events", event); err != nil {
        return nil, err
    }
    
    // Commit transaction
    if err := tx.Commit(); err != nil {
        return nil, err
    }
    
    return pattern, nil
}
```

### 5. Web Frontend (TypeScript/React)

#### Creating Components
```typescript
// web/src/components/PatternCard.tsx
import React from 'react';
import { Pattern } from '@/types/pattern';
import { usePattern } from '@/hooks/usePattern';
import { Card, Badge, Button } from '@/components/ui';

interface PatternCardProps {
  pattern: Pattern;
  onPurchase?: (pattern: Pattern) => void;
}

export const PatternCard: React.FC<PatternCardProps> = ({ 
  pattern, 
  onPurchase 
}) => {
  const { isPurchased, isLoading } = usePattern(pattern.id);
  
  const handlePurchase = async () => {
    if (onPurchase) {
      onPurchase(pattern);
    }
  };
  
  return (
    <Card className="pattern-card">
      <Card.Header>
        <h3>{pattern.name}</h3>
        <Badge variant={pattern.verified ? 'success' : 'default'}>
          {pattern.verified ? 'Verified' : 'Community'}
        </Badge>
      </Card.Header>
      
      <Card.Body>
        <p className="description">{pattern.description}</p>
        
        <div className="stats">
          <span>Used by {pattern.usageCount} projects</span>
          <span>{pattern.rating}/5 rating</span>
        </div>
        
        <div className="code-preview">
          <pre>{pattern.preview}</pre>
        </div>
      </Card.Body>
      
      <Card.Footer>
        {isPurchased ? (
          <Button variant="secondary" disabled>
            Already Purchased
          </Button>
        ) : (
          <Button 
            onClick={handlePurchase}
            loading={isLoading}
            variant="primary"
          >
            Purchase for ${pattern.price}
          </Button>
        )}
      </Card.Footer>
    </Card>
  );
};

// Custom hook for pattern data
// web/src/hooks/usePattern.ts
import { useQuery, useMutation } from '@tanstack/react-query';
import { patternApi } from '@/api/pattern';

export const usePattern = (patternId: string) => {
  const { data: pattern, isLoading } = useQuery({
    queryKey: ['pattern', patternId],
    queryFn: () => patternApi.getPattern(patternId),
  });
  
  const purchaseMutation = useMutation({
    mutationFn: () => patternApi.purchasePattern(patternId),
    onSuccess: () => {
      // Invalidate and refetch
      queryClient.invalidateQueries(['pattern', patternId]);
    },
  });
  
  return {
    pattern,
    isLoading,
    isPurchased: pattern?.purchased ?? false,
    purchase: purchaseMutation.mutate,
  };
};
```

## Database Implementation

### Spanner Schema
```sql
-- patterns table
CREATE TABLE patterns (
  id STRING(36) NOT NULL,
  user_id STRING(36) NOT NULL,
  name STRING(255) NOT NULL,
  description STRING(MAX),
  price NUMERIC NOT NULL,
  status STRING(50) NOT NULL,
  created_at TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp=true),
  updated_at TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp=true),
  CONSTRAINT pk_patterns PRIMARY KEY (id),
  CONSTRAINT fk_patterns_users FOREIGN KEY (user_id) REFERENCES users (id)
) PRIMARY KEY (id);

-- Create indexes
CREATE INDEX idx_patterns_user_id ON patterns(user_id);
CREATE INDEX idx_patterns_status ON patterns(status);
CREATE INDEX idx_patterns_created_at ON patterns(created_at DESC);
```

### BigQuery Analytics
```sql
-- Create analysis results table
CREATE TABLE `ccl-production.analytics.pattern_usage` (
  pattern_id STRING NOT NULL,
  repository_id STRING NOT NULL,
  usage_count INT64 NOT NULL,
  last_used TIMESTAMP NOT NULL,
  performance_metrics STRUCT<
    avg_execution_time_ms FLOAT64,
    success_rate FLOAT64,
    error_count INT64
  >,
  analysis_date DATE NOT NULL
)
PARTITION BY analysis_date
CLUSTER BY pattern_id, repository_id;

-- Query pattern performance
WITH pattern_stats AS (
  SELECT 
    pattern_id,
    COUNT(DISTINCT repository_id) as unique_repos,
    SUM(usage_count) as total_usage,
    AVG(performance_metrics.avg_execution_time_ms) as avg_execution_time,
    AVG(performance_metrics.success_rate) as avg_success_rate
  FROM `ccl-production.analytics.pattern_usage`
  WHERE analysis_date >= DATE_SUB(CURRENT_DATE(), INTERVAL 30 DAY)
  GROUP BY pattern_id
)
SELECT 
  p.name,
  ps.*,
  p.price * ps.total_usage * 0.3 as estimated_revenue
FROM pattern_stats ps
JOIN `ccl-production.transactional.patterns` p ON ps.pattern_id = p.id
ORDER BY total_usage DESC
LIMIT 100;
```

## API Implementation

### REST API Endpoints
```yaml
# api/openapi/patterns.yaml
paths:
  /patterns:
    get:
      summary: List patterns
      parameters:
        - name: category
          in: query
          schema:
            type: string
        - name: sort
          in: query
          schema:
            type: string
            enum: [popularity, rating, price, recent]
      responses:
        200:
          description: List of patterns
          content:
            application/json:
              schema:
                type: object
                properties:
                  patterns:
                    type: array
                    items:
                      $ref: '#/components/schemas/Pattern'
                  pagination:
                    $ref: '#/components/schemas/Pagination'
    
    post:
      summary: Create new pattern
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreatePatternRequest'
      responses:
        201:
          description: Pattern created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Pattern'
```

### GraphQL Schema
```graphql
# api/graphql/schema.graphql
type Pattern {
  id: ID!
  name: String!
  description: String!
  price: Float!
  author: User!
  category: PatternCategory!
  tags: [String!]!
  verified: Boolean!
  rating: Float!
  usageCount: Int!
  preview: String!
  createdAt: DateTime!
  updatedAt: DateTime!
}

type Query {
  pattern(id: ID!): Pattern
  patterns(
    filter: PatternFilter
    sort: PatternSort
    pagination: PaginationInput
  ): PatternConnection!
  
  myPatterns: [Pattern!]!
  purchasedPatterns: [Pattern!]!
}

type Mutation {
  createPattern(input: CreatePatternInput!): Pattern!
  updatePattern(id: ID!, input: UpdatePatternInput!): Pattern!
  purchasePattern(id: ID!): PurchaseResult!
  ratePattern(id: ID!, rating: Int!): Pattern!
}

input PatternFilter {
  category: PatternCategory
  minPrice: Float
  maxPrice: Float
  verified: Boolean
  author: ID
}
```

## Testing Strategy

### Unit Tests
```bash
# Run all unit tests
make test-unit

# Run specific service tests
make test-unit-analysis
make test-unit-query
make test-unit-marketplace
```

### Integration Tests
```python
# tests/integration/test_query_flow.py
import pytest
from tests.fixtures import create_test_repository

@pytest.mark.integration
async def test_complete_query_flow(ccl_client, test_repository):
    """Test complete query processing flow."""
    # Index repository
    analysis = await ccl_client.analyze_repository(test_repository.url)
    assert analysis.status == "completed"
    
    # Query the repository
    result = await ccl_client.query(
        "Find all database connection patterns",
        repository_id=test_repository.id
    )
    
    assert result.confidence > 0.8
    assert len(result.references) > 0
    assert "database" in result.answer.lower()
    
    # Verify pattern detection
    patterns = await ccl_client.get_patterns(test_repository.id)
    db_patterns = [p for p in patterns if "database" in p.name.lower()]
    assert len(db_patterns) > 0
```

### End-to-End Tests
```typescript
// e2e/pattern-purchase.spec.ts
import { test, expect } from '@playwright/test';

test('complete pattern purchase flow', async ({ page }) => {
  // Login
  await page.goto('/login');
  await page.fill('[name="email"]', '<EMAIL>');
  await page.fill('[name="password"]', 'password');
  await page.click('button[type="submit"]');
  
  // Navigate to marketplace
  await page.goto('/marketplace');
  
  // Search for pattern
  await page.fill('[placeholder="Search patterns..."]', 'repository pattern');
  await page.keyboard.press('Enter');
  
  // Click first pattern
  await page.click('.pattern-card:first-child');
  
  // Purchase pattern
  await page.click('button:has-text("Purchase for $")');
  
  // Confirm purchase
  await page.click('button:has-text("Confirm Purchase")');
  
  // Verify success
  await expect(page.locator('.toast-success')).toContainText('Pattern purchased successfully');
  await expect(page.locator('button:has-text("Already Purchased")')).toBeVisible();
});
```

## Deployment

### Service Deployment
```bash
# Deploy a specific service
make deploy-service SERVICE=analysis-engine ENV=production

# Deploy all services
make deploy-all ENV=production

# Rollback deployment
make rollback SERVICE=analysis-engine VERSION=v1.2.3
```

### Infrastructure Updates
```bash
# Plan infrastructure changes
cd infrastructure/environments/production
terraform plan -out=tfplan

# Apply changes
terraform apply tfplan

# Update Cloud Run services
gcloud run deploy analysis-engine \
  --image gcr.io/ccl-production/analysis-engine:latest \
  --platform managed \
  --region us-central1 \
  --memory 2Gi \
  --cpu 2 \
  --max-instances 100 \
  --min-instances 2
```

## Monitoring and Debugging

### Distributed Tracing
```python
# Add tracing to Python services
from opentelemetry import trace
from opentelemetry.trace import Status, StatusCode

tracer = trace.get_tracer(__name__)

class QueryHandler:
    async def handle(self, query: str, context: QueryContext) -> QueryResult:
        with tracer.start_as_current_span(
            "handle_query",
            attributes={
                "query.text": query,
                "repository.id": context.repository_id,
            }
        ) as span:
            try:
                result = await self._process_query(query, context)
                span.set_attribute("query.confidence", result.confidence)
                return result
            except Exception as e:
                span.set_status(Status(StatusCode.ERROR, str(e)))
                span.record_exception(e)
                raise
```

### Structured Logging
```go
// Use structured logging in Go services
import (
    "github.com/rs/zerolog/log"
)

func (s *PatternService) CreatePattern(ctx context.Context, req *CreatePatternRequest) (*Pattern, error) {
    logger := log.With().
        Str("operation", "create_pattern").
        Str("user_id", req.UserID).
        Str("pattern_name", req.Name).
        Logger()
    
    logger.Info().Msg("Creating pattern")
    
    pattern, err := s.repo.Create(ctx, req)
    if err != nil {
        logger.Error().Err(err).Msg("Failed to create pattern")
        return nil, err
    }
    
    logger.Info().
        Str("pattern_id", pattern.ID).
        Msg("Pattern created successfully")
    
    return pattern, nil
}
```

## Performance Optimization

### Caching Strategy
```python
# Implement multi-level caching
from cachetools import TTLCache
import redis.asyncio as redis

class CachedQueryHandler:
    def __init__(self):
        self.memory_cache = TTLCache(maxsize=1000, ttl=300)
        self.redis_client = redis.from_url("redis://localhost")
    
    async def get_cached_result(self, query_hash: str) -> Optional[QueryResult]:
        # Check memory cache first
        if query_hash in self.memory_cache:
            return self.memory_cache[query_hash]
        
        # Check Redis cache
        cached = await self.redis_client.get(f"query:{query_hash}")
        if cached:
            result = QueryResult.from_json(cached)
            self.memory_cache[query_hash] = result
            return result
        
        return None
    
    async def cache_result(self, query_hash: str, result: QueryResult):
        # Cache in memory
        self.memory_cache[query_hash] = result
        
        # Cache in Redis with longer TTL
        await self.redis_client.setex(
            f"query:{query_hash}",
            3600,  # 1 hour
            result.to_json()
        )
```

### Query Optimization
```sql
-- Optimize Spanner queries with proper indexes
CREATE INDEX idx_patterns_composite 
ON patterns(status, created_at DESC)
STORING (name, price, user_id);

-- Use query hints for better performance
@{FORCE_INDEX=idx_patterns_composite}
SELECT p.id, p.name, p.price, u.username
FROM patterns p
JOIN users u ON p.user_id = u.id
WHERE p.status = 'ACTIVE'
  AND p.created_at > TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 7 DAY)
ORDER BY p.created_at DESC
LIMIT 100;
```

## Security Implementation

### Authentication Flow
```typescript
// Implement secure authentication
import { auth } from '@/lib/auth';

export async function authenticateRequest(req: Request): Promise<User> {
  const token = req.headers.get('Authorization')?.replace('Bearer ', '');
  
  if (!token) {
    throw new AuthError('No token provided');
  }
  
  try {
    // Verify JWT token
    const decoded = await auth.verifyToken(token);
    
    // Check token expiration
    if (decoded.exp < Date.now() / 1000) {
      throw new AuthError('Token expired');
    }
    
    // Verify user still exists and is active
    const user = await userService.getUser(decoded.sub);
    if (!user || !user.active) {
      throw new AuthError('Invalid user');
    }
    
    return user;
  } catch (error) {
    throw new AuthError('Invalid token');
  }
}
```

### Input Validation
```python
# Implement comprehensive input validation
from pydantic import BaseModel, validator, constr
from typing import Optional

class CreatePatternRequest(BaseModel):
    name: constr(min_length=3, max_length=100)
    description: constr(min_length=10, max_length=1000)
    category: PatternCategory
    price: float
    artifact_data: bytes
    
    @validator('price')
    def validate_price(cls, v):
        if v < 0 or v > 10000:
            raise ValueError('Price must be between 0 and 10000')
        return round(v, 2)
    
    @validator('artifact_data')
    def validate_artifact(cls, v):
        if len(v) > 10 * 1024 * 1024:  # 10MB limit
            raise ValueError('Artifact size exceeds 10MB limit')
        # Validate WASM format
        if not v.startswith(b'\x00asm'):
            raise ValueError('Invalid WASM artifact')
        return v
```

This implementation guide provides concrete patterns and examples for developing features in the CCL platform. Always refer to the service-specific documentation for detailed API references and follow the established patterns to maintain consistency across the codebase.