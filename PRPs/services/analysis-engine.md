name: "Analysis Engine Service Implementation"
description: |
  Implementation of the Rust-based code analysis service that performs AST parsing,
  pattern detection, and code understanding for the CCL platform.

---

## Goal
Implement the Analysis Engine service as the core code analysis component of the CCL platform, capable of parsing multiple programming languages, extracting AST structures, detecting patterns, and generating code embeddings.

## Why
- **Core Platform Capability**: Foundation for all code understanding features
- **Performance Critical**: Must handle 1M+ LOC analysis in <5 minutes
- **Language Agnostic**: Support for 25+ programming languages
- **AI Integration**: Generate embeddings for semantic code search

## What
A high-performance Rust service that:
- Parses source code into Abstract Syntax Trees (AST)
- Detects coding patterns and architectural structures
- Generates semantic embeddings for code chunks
- Provides real-time analysis via REST API
- Scales to handle enterprise-level codebases

### Success Criteria
- [ ] Service handles 25+ programming languages
- [ ] Analysis completes <5 minutes for 1M LOC (SLO requirement)
- [ ] REST API responds <100ms for queries (p95)
- [ ] Pattern detection accuracy >95%
- [ ] Memory usage <4GB per analysis instance
- [ ] Concurrent analysis support: 50+ repositories
- [ ] 99.9% uptime requirement met
- [ ] Integration with other CCL services working

## All Needed Context

### Service Specifications
```yaml
Service Details:
  name: analysis-engine
  language: Rust
  runtime: Cloud Run
  port: 8001
  
Architecture:
  pattern: microservice
  communication: REST + gRPC
  data_store: Spanner + Cloud Storage
  
Performance:
  slo_response_time: <100ms (API p95), <5min (analysis)
  slo_availability: 99.9%
  scaling: 0-1000 instances
  memory: 4GB per instance
  cpu: 4 vCPU per instance
  concurrent_analysis: 50+ repositories
  max_file_size: 50MB per file
  max_repository_size: 10GB
```

### Technology Stack
```yaml
Primary Language: Rust 1.75+
Framework: Axum (async web framework)
Parser: Tree-sitter (multi-language parsing)
Database: Google Cloud Spanner
Storage: Google Cloud Storage
Dependencies:
  - axum: 0.7+ # Web framework
  - tokio: 1.35+ # Async runtime
  - tree-sitter: 0.20+ # Code parsing
  - serde: 1.0+ # Serialization
  - anyhow: 1.0+ # Error handling
  - tracing: 0.1+ # Logging
  - google-cloud-spanner: 0.8+ # Database client
  - tungstenite: 0.21+ # WebSocket support
  - tower-http: 0.4+ # HTTP middleware
  - rayon: 1.8+ # Parallel processing
  - uuid: 1.6+ # UUID generation
  - chrono: 0.4+ # Date/time handling
  - dashmap: 5.5+ # Concurrent HashMap
  
Development Tools:
  - cargo: Build system
  - clippy: Linting
  - rustfmt: Code formatting
  - criterion: Benchmarking
```

### Current Codebase Context
```bash
# Reference implementation patterns from:
examples/analysis-engine/
├── analyzer.rs              # Core analysis patterns
├── ast_parser.rs           # AST parsing implementation
├── error_handling.rs       # Rust error handling patterns
└── pattern_detector.rs     # Pattern detection algorithms
```

### Desired Service Structure
```bash
services/analysis-engine/
├── Cargo.toml              # Rust dependencies
├── Dockerfile              # Container definition
├── cloudbuild.yaml         # Build configuration
├── src/
│   ├── main.rs            # Service entry point
│   ├── lib.rs             # Library root
│   ├── config/            # Configuration management
│   │   ├── mod.rs
│   │   └── settings.rs
│   ├── handlers/          # HTTP request handlers
│   │   ├── mod.rs
│   │   ├── analysis.rs    # Analysis endpoints
│   │   └── health.rs      # Health check
│   ├── services/          # Business logic
│   │   ├── mod.rs
│   │   ├── analyzer.rs    # Core analysis service
│   │   ├── parser.rs      # Code parsing service
│   │   └── embeddings.rs  # Embedding generation
│   ├── models/            # Data models
│   │   ├── mod.rs
│   │   ├── analysis.rs    # Analysis result models
│   │   └── ast.rs         # AST models
│   ├── clients/           # External service clients
│   │   ├── mod.rs
│   │   ├── spanner.rs     # Database client
│   │   └── storage.rs     # Cloud Storage client
│   └── utils/             # Utility functions
│       ├── mod.rs
│       └── metrics.rs     # Performance metrics
├── tests/                 # Test files
│   ├── integration/       # Integration tests
│   ├── unit/             # Unit tests
│   └── fixtures/         # Test data
└── docs/
    ├── README.md         # Service documentation
    └── api.md            # API documentation
```

### Integration Requirements
```yaml
Upstream Dependencies:
  - None (entry point service)
  
Downstream Consumers:
  - query-intelligence: Analysis results for semantic search
  - pattern-mining: AST data for ML training
  - marketplace: Pattern validation
  - web: Analysis status and results
  
Event Publications:
  - analysis.completed: When analysis finishes
  - analysis.failed: When analysis fails
  - pattern.detected: When new patterns found
  
External APIs:
  - Google Cloud Spanner: Analysis result storage
  - Google Cloud Storage: Code artifact storage
  - Vertex AI: Embedding generation
```

### Known Gotchas & Library Quirks
```yaml
Rust-Specific:
  - Memory management: Use Arc<> for shared data across async tasks
  - Error handling: Use anyhow for service errors, thiserror for library errors
  - Async: All I/O operations must be async with proper error handling
  
Tree-sitter:
  - Language parsers must be compiled separately
  - Parser state is not thread-safe, clone for each analysis
  - Large files can cause stack overflow, use streaming parsing
  
Google Cloud:
  - Spanner requires connection pooling for performance
  - Cloud Storage has rate limits, implement exponential backoff
  - Service account authentication required for all GCP services
  
Performance:
  - AST parsing is CPU intensive, use rayon for parallelization
  - Memory usage grows with file size, implement streaming for large files
  - Pattern detection can be expensive, cache results in Spanner
```

## Implementation Blueprint

### Phase 1: Service Foundation
1. **Project Setup**
   ```bash
   cargo new services/analysis-engine --bin
   cd services/analysis-engine
   ```

2. **Core Dependencies**
   - Add Axum for web framework
   - Add Tokio for async runtime
   - Add Tree-sitter for parsing
   - Add Google Cloud clients

3. **Basic Service Structure**
   - Main service entry point
   - Health check endpoint
   - Configuration management
   - Logging setup

### Phase 2: Code Parsing Engine
1. **Tree-sitter Integration**
   - Multi-language parser setup
   - AST extraction logic
   - Error handling for malformed code

2. **Language Support**
   - JavaScript/TypeScript parser
   - Python parser
   - Rust parser
   - Go parser
   - Java parser
   - (Expand to 25+ languages)

3. **AST Processing**
   - AST normalization
   - Metadata extraction
   - Dependency analysis

### Phase 3: Pattern Detection
1. **Pattern Recognition Engine**
   - Design pattern detection
   - Anti-pattern identification
   - Code smell detection

2. **Architectural Analysis**
   - Module dependency mapping
   - Layer violation detection
   - Coupling analysis

3. **Metrics Calculation**
   - Cyclomatic complexity
   - Code coverage analysis
   - Technical debt scoring

### Phase 4: API Implementation
1. **REST API Endpoints**
   ```rust
   // POST /analyze - Analyze code repository
   // GET /analysis/{id} - Get analysis results
   // GET /patterns/{id} - Get detected patterns
   // GET /health - Health check
   ```

2. **Request/Response Models**
   - Analysis request validation
   - Streaming response for large analyses
   - Error response standardization

3. **Authentication & Authorization**
   - API key validation
   - Rate limiting
   - Request logging

### Phase 5: Performance Optimization
1. **Parallel Processing**
   - Multi-threaded file processing
   - Async I/O for database operations
   - Connection pooling

2. **Caching Strategy**
   - Analysis result caching
   - Pattern cache management
   - Memory usage optimization

3. **Monitoring Integration**
   - Performance metrics collection
   - Distributed tracing
   - Error tracking

## Validation Gates

### Development Validation
```bash
# Code Quality
cargo clippy -- -D warnings
cargo fmt --check

# Unit Tests
cargo test --lib

# Integration Tests
cargo test --test integration

# Security Audit
cargo audit

# Performance Benchmarks
cargo bench
```

### API Validation
```bash
# Health Check
curl -f http://localhost:8001/health

# Analysis Endpoint
curl -X POST http://localhost:8001/analyze \
  -H "Content-Type: application/json" \
  -d '{"repository_url": "https://github.com/test/repo"}'

# Load Testing
wrk -t12 -c400 -d30s http://localhost:8001/health
```

### Performance Validation
```bash
# Memory Usage Check
valgrind --tool=massif target/release/analysis-engine

# CPU Profiling
perf record -g target/release/analysis-engine
perf report

# Analysis Performance Test
time curl -X POST http://localhost:8001/analyze \
  -H "Content-Type: application/json" \
  -d '{"code": "$(cat large_file.js)"}'
```

## Success Metrics

### Performance Metrics
- **Analysis Speed**: <30s for 1M LOC
- **API Response Time**: <100ms (p95)
- **Memory Usage**: <4GB per analysis
- **CPU Utilization**: <80% average

### Quality Metrics
- **Pattern Detection Accuracy**: >95%
- **Language Support**: 25+ languages
- **Test Coverage**: >90%
- **Error Rate**: <1%

### Reliability Metrics
- **Uptime**: 99.9%
- **Crash Rate**: <0.1%
- **Recovery Time**: <30s
- **Data Consistency**: 100%

## Final Validation Checklist
- [ ] All unit tests pass (>90% coverage)
- [ ] Integration tests pass
- [ ] Performance benchmarks meet SLO
- [ ] Security audit passes
- [ ] API documentation complete
- [ ] Service documentation updated
- [ ] Monitoring configured
- [ ] Deployment successful
- [ ] Health checks passing
- [ ] Integration with other services verified
- [ ] Load testing completed
- [ ] Error handling validated

---

## Implementation Notes

### Pattern References
- Follow Rust patterns from `examples/analysis-engine/`
- Use async/await patterns consistently
- Implement proper error handling with Result<T, E>
- Use structured logging with tracing crate

### Security Requirements
- Validate all input parameters
- Sanitize code content before processing
- Use secure HTTP headers
- Implement rate limiting
- Add audit logging for all operations

### Monitoring Requirements
- Expose Prometheus metrics
- Add distributed tracing spans
- Log all errors with context
- Monitor memory and CPU usage
- Track analysis performance metrics
