name: "Marketplace Service Implementation"
description: |
  Implementation of the Go-based marketplace service for pattern sharing, validation, monetization,
  and community features with high-performance transaction processing and real-time updates.

---

## Goal
Implement the Marketplace service as the commercial and community hub of the CCL platform, enabling pattern sharing, validation, monetization, user management, and community features with enterprise-grade performance and security.

## Why
- **Revenue Generation**: Core monetization platform for CCL
- **Community Building**: Central hub for developer pattern sharing
- **Quality Assurance**: Pattern validation and curation system
- **Business Growth**: Marketplace drives platform adoption

## What
A high-performance Go service that:
- Manages pattern marketplace with buying/selling
- Handles user accounts and authentication
- Processes payments and subscriptions
- Validates and curates submitted patterns
- Provides community features and ratings
- Manages pattern licensing and distribution

### Success Criteria
- [ ] Transaction processing <200ms (p95)
- [ ] Payment processing integration working
- [ ] Pattern validation system functional
- [ ] User management and authentication secure
- [ ] Community features operational
- [ ] Real-time notifications working
- [ ] Revenue tracking accurate
- [ ] Concurrent transactions: 500+ per second
- [ ] Memory usage <1GB per instance
- [ ] Database query response <50ms
- [ ] 99.9% uptime requirement met

## All Needed Context

### Service Specifications
```yaml
Service Details:
  name: marketplace
  language: Go 1.21+
  runtime: Cloud Run
  port: 8004
  
Architecture:
  pattern: microservice
  communication: REST + gRPC + WebSocket + Events
  data_store: Spanner (marketplace data) + Cloud Storage (assets)
  service_boundaries: strict
  
Performance:
  slo_response_time: <200ms (p95)
  slo_availability: 99.9%
  scaling: 0-500 instances
  memory: 1GB per instance
  cpu: 1 vCPU per instance
  concurrent_transactions: 500+ per second
  database_query_time: <50ms
  payment_processing_time: <3s
```

### Technology Stack
```yaml
Primary Language: Go 1.21+
Framework: Gin (HTTP router)
Database: Google Cloud Spanner (marketplace-specific tables only)
Storage: Google Cloud Storage (pattern assets)
Payment: Stripe API
User Data: Via user management API
Dependencies:
  - gin-gonic/gin: v1.9+ # HTTP web framework
  - google.golang.org/grpc: v1.59+ # gRPC framework
  - cloud.google.com/go/spanner: v1.51+ # Spanner client (marketplace tables only)
  - cloud.google.com/go/storage: v1.35+ # Storage client
  - stripe/stripe-go: v75+ # Payment processing
  - golang-jwt/jwt: v5+ # JWT authentication
  - gorilla/websocket: v1.5+ # WebSocket support
  - go.uber.org/zap: v1.26+ # Structured logging
  - prometheus/client_golang: v1.17+ # Metrics
  - github.com/redis/go-redis/v9: v9.3+ # Redis client
  - github.com/google/uuid: v1.4+ # UUID generation
  - golang.org/x/crypto: v0.17+ # Cryptographic functions
  - github.com/go-playground/validator/v10: v10.16+ # Input validation
  - cloud.google.com/go/pubsub: v1.33+ # Event messaging
  - github.com/sony/gobreaker: v0.5+ # Circuit breaker
  
Development Tools:
  - go test: Testing framework
  - golangci-lint: Linting
  - gofmt: Code formatting
  - go mod: Dependency management
```

### Current Codebase Context
```bash
# Reference implementation patterns from:
examples/marketplace/
├── handlers.go             # HTTP handler patterns
├── payment_processing.go   # Payment integration patterns
├── pattern_validation.go   # Pattern validation logic
└── user_management.go      # User management patterns
```

### Desired Service Structure
```bash
services/marketplace/
├── go.mod                  # Go dependencies
├── go.sum                  # Dependency checksums
├── Dockerfile              # Container definition
├── cloudbuild.yaml         # Build configuration
├── cmd/
│   └── marketplace/        # Main application
│       └── main.go
├── internal/               # Private packages
│   ├── config/             # Configuration
│   │   └── config.go
│   ├── handlers/           # HTTP handlers
│   │   ├── auth.go         # Authentication handlers
│   │   ├── patterns.go     # Pattern marketplace handlers
│   │   ├── payments.go     # Payment handlers
│   │   ├── users.go        # User management handlers
│   │   └── websocket.go    # WebSocket handlers
│   ├── services/           # Business logic
│   │   ├── auth_service.go # Authentication service
│   │   ├── pattern_service.go # Pattern management
│   │   ├── payment_service.go # Payment processing
│   │   ├── user_service.go    # User management
│   │   └── notification_service.go # Notifications
│   ├── models/             # Data models
│   │   ├── user.go         # User models
│   │   ├── pattern.go      # Pattern models
│   │   ├── transaction.go  # Transaction models
│   │   └── notification.go # Notification models
│   ├── clients/            # External service clients
│   │   ├── spanner.go      # Spanner client
│   │   ├── firestore.go    # Firestore client
│   │   ├── storage.go      # Storage client
│   │   └── stripe.go       # Stripe client
│   ├── middleware/         # HTTP middleware
│   │   ├── auth.go         # Authentication middleware
│   │   ├── cors.go         # CORS middleware
│   │   ├── logging.go      # Logging middleware
│   │   └── metrics.go      # Metrics middleware
│   └── utils/              # Utility functions
│       ├── validation.go   # Input validation
│       └── crypto.go       # Cryptographic utilities
├── pkg/                    # Public packages
│   └── client/             # Client library
│       └── marketplace.go
├── api/                    # API definitions
│   ├── openapi.yaml        # REST API spec
│   └── proto/              # gRPC definitions
│       └── marketplace.proto
├── tests/                  # Test files
│   ├── unit/              # Unit tests
│   ├── integration/       # Integration tests
│   └── fixtures/          # Test data
└── docs/
    ├── README.md          # Service documentation
    └── api.md             # API documentation
```

### API Endpoints Specification

#### Marketplace Pattern Search
```http
GET /marketplace/patterns?q=authentication&category=security&language=javascript&sort=popular
```

**Query Parameters:**
| Parameter | Type | Description |
|-----------|------|-------------|
| `q` | string | Search query |
| `category` | string | Category filter |
| `language` | string | Language filter |
| `author` | string | Author filter |
| `min_rating` | float | Minimum rating (1-5) |
| `price_range` | string | free, paid, 0-10, 10-50, 50+ |
| `sort` | string | popular, recent, rating, price_asc, price_desc |

#### Pattern Purchase
```http
POST /marketplace/purchase
Content-Type: application/json

{
  "pattern_id": "mkt_pat_123",
  "payment_method_id": "pm_1234567890",
  "license_type": "team"
}
```

**Response:**
```json
{
  "purchase_id": "pur_abc123",
  "pattern_id": "mkt_pat_123",
  "status": "completed",
  "amount": 9.99,
  "currency": "USD",
  "license": {
    "type": "team",
    "seats": 10,
    "expires_at": null,
    "key": "LIC-XXXX-XXXX-XXXX"
  },
  "download_url": "https://api.ccl.dev/v1/downloads/pur_abc123",
  "receipt_url": "https://ccl.dev/receipts/pur_abc123"
}
```

#### Pattern Publishing
```http
POST /marketplace/patterns
Content-Type: application/json

{
  "name": "Microservices Communication Pattern",
  "description": "Battle-tested patterns for service-to-service communication",
  "category": "architecture",
  "price": 19.99,
  "license": "single-use",
  "pattern": {
    // Pattern definition
  },
  "documentation": "# Comprehensive docs here",
  "demo_repository": "https://github.com/author/pattern-demo",
  "support_email": "<EMAIL>"
}
```

### Integration Requirements
```yaml
Upstream Dependencies:
  - pattern-mining: Pattern validation and scoring
  - query-intelligence: Pattern usage analytics
  - auth: User authentication and authorization
  
Downstream Consumers:
  - web: Marketplace UI and user interactions
  - collaboration: Pattern sharing in teams
  - analytics: Revenue and usage analytics
  
Event Subscriptions:
  - pattern.validated: Pattern validation results
  - user.created: New user registration
  - payment.completed: Payment processing results
  
Event Publications:
  - pattern.purchased: Pattern purchase completed
  - pattern.published: New pattern published
  - user.upgraded: User subscription upgraded
  
External APIs:
  - Stripe: Payment processing and subscription management
  - SendGrid: Email notifications
  - Google Cloud Spanner: Transaction data
  - Firestore: Real-time user sessions
  - Cloud Storage: Pattern file storage
```

### Known Gotchas & Library Quirks
```yaml
Go-Specific:
  - Error handling: Always check errors, use structured error types
  - Goroutines: Be careful with goroutine leaks, use context for cancellation
  - Memory management: Go GC is good but watch for memory leaks in long-running services
  
Gin Framework:
  - Middleware order: Authentication before authorization
  - Context handling: Use gin.Context properly for request scoping
  - JSON binding: Validate input data thoroughly
  
Spanner:
  - Transactions: Use read-write transactions sparingly, prefer read-only
  - Hotspotting: Avoid sequential keys, use UUID for primary keys
  - Batch operations: Use batch mutations for better performance
  
Stripe Integration:
  - Webhooks: Verify webhook signatures for security
  - Idempotency: Use idempotency keys for payment operations
  - Error handling: Handle Stripe-specific error types properly
  
Performance:
  - Connection pooling: Reuse database connections
  - Caching: Cache frequently accessed data
  - Concurrent processing: Use goroutines for I/O operations
```

## Implementation Blueprint

### Phase 1: Service Foundation
1. **Project Setup**
   ```bash
   mkdir -p services/marketplace
   cd services/marketplace
   go mod init github.com/ccl-platform/marketplace
   ```

2. **Core Dependencies**
   - Gin for HTTP routing
   - Spanner and Firestore clients
   - Stripe for payment processing
   - JWT for authentication

3. **Basic Service Structure**
   - Main application entry point
   - Configuration management
   - Health check endpoints
   - Logging and metrics

### Phase 2: User Management System
1. **Authentication & Authorization**
   - JWT token validation
   - Role-based access control
   - API key management
   - Session management

2. **User Profile Management**
   - User registration and login
   - Profile management
   - Subscription management
   - Preference settings

3. **Account Security**
   - Password hashing and validation
   - Two-factor authentication
   - Account recovery
   - Security audit logging

### Phase 3: Pattern Marketplace
1. **Pattern Management**
   - Pattern listing and search
   - Pattern categorization
   - Version management
   - License management

2. **Pattern Validation**
   - Automated pattern validation
   - Community review system
   - Quality scoring
   - Approval workflow

3. **Pattern Distribution**
   - Download management
   - Access control
   - Usage tracking
   - License enforcement

### Phase 4: Payment Processing
1. **Stripe Integration**
   - Payment method management
   - Subscription processing
   - Invoice generation
   - Refund handling

2. **Transaction Management**
   - Purchase processing
   - Revenue tracking
   - Payout management
   - Financial reporting

3. **Billing System**
   - Subscription billing
   - Usage-based billing
   - Tax calculation
   - Payment notifications

### Phase 5: Community Features
1. **Rating and Reviews**
   - Pattern rating system
   - Review management
   - Reputation scoring
   - Moderation tools

2. **Social Features**
   - User following
   - Pattern collections
   - Activity feeds
   - Notifications

3. **Analytics and Insights**
   - Usage analytics
   - Revenue analytics
   - User behavior tracking
   - Performance metrics

## Validation Gates

### Development Validation
```bash
# Code Quality
golangci-lint run ./...
gofmt -s -w .
go vet ./...

# Unit Tests
go test ./... -v -cover

# Integration Tests
go test ./tests/integration/... -v

# Security Scan
gosec ./...

# Performance Tests
go test -bench=. ./tests/performance/...
```

### API Validation
```bash
# Health Check
curl -f http://localhost:8004/health

# Authentication Test
curl -X POST http://localhost:8004/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "password"}'

# Pattern Listing
curl -H "Authorization: Bearer $TOKEN" \
  http://localhost:8004/patterns

# Payment Test (Stripe Test Mode)
curl -X POST http://localhost:8004/payments/purchase \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"pattern_id": "test-pattern", "payment_method": "pm_card_visa"}'
```

### Payment Integration Validation
```bash
# Stripe Webhook Test
stripe listen --forward-to localhost:8004/webhooks/stripe

# Payment Processing Test
go test ./tests/integration/payment_test.go -v

# Subscription Test
go test ./tests/integration/subscription_test.go -v
```

## Success Metrics

### Performance Metrics
- **Transaction Processing Time**: <200ms (p95)
- **API Response Time**: <100ms (p95)
- **Payment Processing Success Rate**: >99.5%
- **Memory Usage**: <1GB per instance

### Business Metrics
- **Transaction Success Rate**: >99%
- **Payment Failure Rate**: <1%
- **User Registration Conversion**: >80%
- **Pattern Purchase Conversion**: >15%

### Quality Metrics
- **Pattern Validation Accuracy**: >95%
- **User Satisfaction**: >4.5/5
- **Error Rate**: <0.5%
- **Test Coverage**: >85%

## Final Validation Checklist
- [ ] All unit tests pass (>85% coverage)
- [ ] Integration tests pass
- [ ] Payment processing working
- [ ] Stripe integration validated
- [ ] User authentication secure
- [ ] Pattern validation functional
- [ ] Performance benchmarks met
- [ ] Security scan passes
- [ ] API documentation complete
- [ ] Service documentation updated
- [ ] Monitoring configured
- [ ] Deployment successful

---

## Implementation Notes

### Go Best Practices
- Use structured logging with zap
- Implement proper error handling with custom error types
- Use context for request scoping and cancellation
- Follow Go naming conventions and package structure

### Payment Security
- Always validate webhook signatures
- Use HTTPS for all payment-related endpoints
- Implement proper PCI compliance measures
- Store minimal payment information

### Performance Optimization
- Use connection pooling for database clients
- Implement caching for frequently accessed data
- Use goroutines for concurrent processing
- Monitor and optimize database queries

### Marketplace Features
- Implement comprehensive pattern validation
- Provide detailed analytics for sellers
- Create engaging community features
- Ensure fair and transparent pricing
