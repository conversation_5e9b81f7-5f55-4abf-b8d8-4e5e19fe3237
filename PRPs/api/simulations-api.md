# PRP: Simulations API

## 1. Overview

This document specifies the Simulations API endpoint. This API allows for the simulation of architectural changes to a codebase, providing insights into feasibility, effort, risk, and potential impact.

## 2. Endpoint Specification

### `POST /simulate`

This endpoint runs a simulation of a proposed architectural change.

#### Request Body

```json
{
  "repository_id": "repo_0987654321fedcba",
  "simulation_type": "refactoring",
  "changes": {
    "description": "Convert monolith to microservices",
    "target_services": [
      {
        "name": "AuthService",
        "components": ["auth", "users", "permissions"]
      },
      {
        "name": "PaymentService",
        "components": ["payments", "billing", "invoices"]
      }
    ],
    "constraints": {
      "preserve_api": true,
      "max_downtime_hours": 2,
      "gradual_migration": true
    }
  }
}
```

-   **`repository_id`** (string, required): The ID of the repository to run the simulation on.
-   **`simulation_type`** (string, required): The type of simulation (e.g., `refactoring`, `new_feature`, `dependency_upgrade`).
-   **`changes`** (object, required): An object describing the proposed changes.
    -   **`description`** (string, required): A high-level description of the change.
    -   **`target_services`** (array of objects, optional): For microservice refactoring, a list of target services and their components.
    -   **`constraints`** (object, optional): A set of constraints for the simulation.

#### Response Body

```json
{
  "simulation_id": "sim_xyz123",
  "feasibility": 0.85,
  "estimated_effort": {
    "developer_days": 45,
    "calendar_days": 21,
    "team_size": 3
  },
  "risk_assessment": {
    "overall_risk": "medium",
    "factors": [
      {
        "factor": "Data consistency",
        "risk": "high",
        "mitigation": "Implement distributed transactions"
      }
    ]
  },
  "migration_plan": {
    "phases": [
      {
        "phase": 1,
        "name": "Extract Authentication",
        "duration_days": 7,
        "tasks": [
          "Create AuthService repository",
          "Move auth components",
          "Update API gateway"
        ]
      }
    ]
  },
  "impact_analysis": {
    "affected_files": 234,
    "affected_tests": 567,
    "api_changes": 12,
    "breaking_changes": 0
  }
}
```

-   **`simulation_id`** (string): A unique identifier for the simulation.
-   **`feasibility`** (float): A score from 0 to 1 indicating the feasibility of the proposed change.
-   **`estimated_effort`** (object): An estimation of the effort required.
-   **`risk_assessment`** (object): An analysis of the risks involved.
-   **`migration_plan`** (object): A high-level plan for implementing the change.
-   **`impact_analysis`** (object): An analysis of the impact on the existing codebase.

## 3. Success Criteria

-   The API must be able to simulate a variety of architectural changes.
-   The simulation results must be detailed and actionable.
-   The API must return a `202 Accepted` status on starting a simulation and provide a way to retrieve the results later.
-   The response must include a feasibility score, effort estimation, risk assessment, migration plan, and impact analysis.
