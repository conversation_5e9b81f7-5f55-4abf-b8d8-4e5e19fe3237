name: "REST API Implementation"
description: |
  Implementation of the comprehensive REST API for the CCL platform including all endpoints,
  authentication, validation, error handling, and API documentation with OpenAPI specification.

---

## Goal
Implement a complete, production-ready REST API for the CCL platform that provides all necessary endpoints for code analysis, pattern management, user operations, and marketplace functionality with enterprise-grade security and performance.

## Why
- **Primary Interface**: Main way external systems interact with CCL
- **Developer Experience**: Clean, well-documented API for easy integration
- **Business Critical**: API drives all user interactions and integrations
- **Scalability**: Must handle high-volume requests efficiently

## What
A comprehensive REST API that:
- Provides all core CCL functionality via HTTP endpoints
- Implements proper authentication and authorization
- Includes comprehensive input validation and error handling
- Supports API versioning and backward compatibility
- Provides detailed OpenAPI documentation
- Implements rate limiting and security measures

### Success Criteria
- [ ] All API endpoints implemented and functional
- [ ] Authentication and authorization working
- [ ] Input validation comprehensive
- [ ] Error handling consistent across all endpoints
- [ ] API documentation complete with OpenAPI spec
- [ ] Rate limiting and security measures active
- [ ] Performance meets SLO (<100ms p95)
- [ ] API versioning strategy implemented

## All Needed Context

### API Specifications
```yaml
API Details:
  base_url: https://api.ccl.dev/v1
  protocol: HTTPS only
  format: JSON (application/json)
  authentication: Bearer token (JWT)
  versioning: URL path versioning (/v1, /v2)
  
Performance Requirements:
  response_time: <100ms (p95)
  availability: 99.9%
  rate_limit: 1000 requests/minute per API key
  
Security:
  authentication: JWT tokens
  authorization: Role-based access control
  encryption: TLS 1.3 minimum
  input_validation: Comprehensive validation
```

### Current Codebase Context
```bash
# Reference API patterns from:
docs/api/README.md           # Complete API specification
examples/api/
├── authentication.js        # Auth implementation patterns
├── validation.js           # Input validation patterns
├── error_handling.js       # Error handling patterns
└── rate_limiting.js        # Rate limiting implementation
```

### API Endpoint Categories
```yaml
Authentication Endpoints:
  - POST /auth/login
  - POST /auth/logout
  - POST /auth/refresh
  - GET /auth/me
  
Analysis Endpoints:
  - POST /analyze
  - GET /analysis/{id}
  - GET /analysis/{id}/status
  - DELETE /analysis/{id}
  
Pattern Endpoints:
  - GET /patterns
  - GET /patterns/{id}
  - POST /patterns
  - PUT /patterns/{id}
  - DELETE /patterns/{id}
  
Query Endpoints:
  - POST /query
  - GET /conversations
  - GET /conversations/{id}
  - POST /conversations/{id}/messages
  
Marketplace Endpoints:
  - GET /marketplace/patterns
  - POST /marketplace/patterns/{id}/purchase
  - GET /marketplace/patterns/{id}/reviews
  - POST /marketplace/patterns/{id}/reviews
  
User Management:
  - GET /users/profile
  - PUT /users/profile
  - GET /users/usage
  - GET /users/api-keys
  - POST /users/api-keys
```

### Authentication & Authorization
```yaml
Authentication Methods:
  api_key:
    type: API Key
    location: Authorization header
    format: "Bearer {api_key}"
    
  oauth2:
    type: OAuth 2.0
    flows:
      authorization_code:
        authorization_url: https://auth.ccl.dev/oauth/authorize
        token_url: https://auth.ccl.dev/oauth/token
        
API Key Scopes:
  read:analysis: Read analysis results
  write:analysis: Create analyses
  read:patterns: Read patterns
  write:patterns: Create/modify patterns
  read:queries: Read conversations
  write:queries: Create queries
  admin: Full access
  
Rate Limits by Tier:
  free: 100 requests/hour
  pro: 1000 requests/hour
  team: 10000 requests/hour
  enterprise: unlimited
```

### Error Handling Standards
```yaml
Error Response Format:
  error:
    code: string          # Machine-readable error code
    message: string       # Human-readable error message
    details: object       # Additional error details
    request_id: string    # Request ID for support
    
HTTP Status Codes:
  200: Success
  201: Created
  400: Bad Request (validation errors)
  401: Unauthorized (authentication required)
  403: Forbidden (insufficient permissions)
  404: Not Found
  409: Conflict (resource already exists)
  422: Unprocessable Entity (business logic error)
  429: Too Many Requests (rate limit exceeded)
  500: Internal Server Error
  503: Service Unavailable
  
Common Error Codes:
  INVALID_REQUEST: Request validation failed
  UNAUTHORIZED: Authentication required
  FORBIDDEN: Insufficient permissions
  NOT_FOUND: Resource not found
  RATE_LIMIT_EXCEEDED: Too many requests
  ANALYSIS_FAILED: Code analysis failed
  PATTERN_NOT_FOUND: Pattern not found
  INSUFFICIENT_CREDITS: Not enough credits
```

## Implementation Blueprint

### Phase 1: API Foundation
1. **API Gateway Setup**
   ```yaml
   # API Gateway configuration
   swagger: "3.0.0"
   info:
     title: CCL Platform API
     version: "1.0.0"
     description: Codebase Context Layer Platform API
   
   servers:
     - url: https://api.ccl.dev/v1
       description: Production server
     - url: https://api-staging.ccl.dev/v1
       description: Staging server
   ```

2. **Authentication Middleware**
   ```javascript
   // Authentication middleware implementation
   const authenticateToken = async (req, res, next) => {
     const authHeader = req.headers['authorization'];
     const token = authHeader && authHeader.split(' ')[1];
     
     if (!token) {
       return res.status(401).json({
         error: {
           code: 'UNAUTHORIZED',
           message: 'Access token required'
         }
       });
     }
     
     try {
       const decoded = jwt.verify(token, process.env.JWT_SECRET);
       req.user = decoded;
       next();
     } catch (error) {
       return res.status(403).json({
         error: {
           code: 'FORBIDDEN',
           message: 'Invalid or expired token'
         }
       });
     }
   };
   ```

3. **Input Validation Framework**
   ```javascript
   // Input validation using Joi
   const analysisSchema = Joi.object({
     repository_url: Joi.string().uri().required(),
     branch: Joi.string().default('main'),
     include_patterns: Joi.array().items(Joi.string()),
     exclude_patterns: Joi.array().items(Joi.string()),
     analysis_type: Joi.string().valid('full', 'incremental').default('full')
   });
   ```

### Phase 2: Core API Endpoints
1. **Analysis Endpoints**
   ```javascript
   // POST /analyze
   app.post('/analyze', authenticateToken, validateInput(analysisSchema), async (req, res) => {
     try {
       const analysis = await analysisService.startAnalysis(req.body, req.user);
       res.status(201).json({
         id: analysis.id,
         status: 'started',
         estimated_completion: analysis.estimatedCompletion,
         webhook_url: analysis.webhookUrl
       });
     } catch (error) {
       handleApiError(error, res);
     }
   });
   
   // GET /analysis/{id}
   app.get('/analysis/:id', authenticateToken, async (req, res) => {
     try {
       const analysis = await analysisService.getAnalysis(req.params.id, req.user);
       res.json(analysis);
     } catch (error) {
       handleApiError(error, res);
     }
   });
   ```

2. **Pattern Endpoints**
   ```javascript
   // GET /patterns
   app.get('/patterns', authenticateToken, async (req, res) => {
     try {
       const patterns = await patternService.searchPatterns(req.query, req.user);
       res.json({
         patterns: patterns.items,
         pagination: patterns.pagination,
         filters: patterns.appliedFilters
       });
     } catch (error) {
       handleApiError(error, res);
     }
   });
   ```

3. **Query Endpoints**
   ```javascript
   // POST /query
   app.post('/query', authenticateToken, async (req, res) => {
     try {
       const result = await queryService.processQuery(req.body.query, req.user);
       res.json({
         answer: result.answer,
         confidence: result.confidence,
         sources: result.sources,
         conversation_id: result.conversationId
       });
     } catch (error) {
       handleApiError(error, res);
     }
   });
   ```

### Phase 3: Advanced Features
1. **Rate Limiting**
   ```javascript
   const rateLimit = require('express-rate-limit');
   
   const createRateLimiter = (windowMs, max) => {
     return rateLimit({
       windowMs,
       max,
       message: {
         error: {
           code: 'RATE_LIMIT_EXCEEDED',
           message: 'Too many requests, please try again later'
         }
       },
       standardHeaders: true,
       legacyHeaders: false
     });
   };
   
   // Apply different limits based on user tier
   app.use('/analyze', createRateLimiter(60 * 60 * 1000, getUserRateLimit));
   ```

2. **API Versioning**
   ```javascript
   // Version-specific routing
   app.use('/v1', v1Router);
   app.use('/v2', v2Router);
   
   // Backward compatibility middleware
   const handleVersionCompatibility = (req, res, next) => {
     const version = req.path.split('/')[1];
     if (version === 'v1') {
       // Apply v1 compatibility transformations
       req.body = transformV1Request(req.body);
     }
     next();
   };
   ```

3. **Webhook Support**
   ```javascript
   // Webhook delivery system
   const deliverWebhook = async (url, payload, secret) => {
     const signature = crypto
       .createHmac('sha256', secret)
       .update(JSON.stringify(payload))
       .digest('hex');
   
     await axios.post(url, payload, {
       headers: {
         'X-CCL-Signature': `sha256=${signature}`,
         'Content-Type': 'application/json'
       },
       timeout: 10000
     });
   };
   ```

### Phase 4: Documentation & Testing
1. **OpenAPI Specification**
   ```yaml
   # Complete OpenAPI spec
   paths:
     /analyze:
       post:
         summary: Start code analysis
         operationId: startAnalysis
         requestBody:
           required: true
           content:
             application/json:
               schema:
                 $ref: '#/components/schemas/AnalysisRequest'
         responses:
           '201':
             description: Analysis started successfully
             content:
               application/json:
                 schema:
                   $ref: '#/components/schemas/AnalysisResponse'
   ```

2. **API Testing Suite**
   ```javascript
   // Comprehensive API tests
   describe('Analysis API', () => {
     test('POST /analyze should start analysis', async () => {
       const response = await request(app)
         .post('/analyze')
         .set('Authorization', `Bearer ${validToken}`)
         .send(validAnalysisRequest)
         .expect(201);
       
       expect(response.body).toHaveProperty('id');
       expect(response.body.status).toBe('started');
     });
   });
   ```

## Validation Gates

### API Functionality Testing
```bash
# Authentication test
curl -X POST https://api.ccl.dev/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "password"}'

# Analysis endpoint test
curl -X POST https://api.ccl.dev/v1/analyze \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"repository_url": "https://github.com/test/repo"}'

# Pattern search test
curl -H "Authorization: Bearer $TOKEN" \
  "https://api.ccl.dev/v1/patterns?category=design-patterns&limit=10"
```

### Performance Testing
```bash
# Load testing with Apache Bench
ab -n 1000 -c 10 -H "Authorization: Bearer $TOKEN" \
  https://api.ccl.dev/v1/patterns

# Rate limiting test
for i in {1..150}; do
  curl -H "Authorization: Bearer $TOKEN" \
    https://api.ccl.dev/v1/patterns
done
```

### Security Testing
```bash
# Test without authentication
curl -X POST https://api.ccl.dev/v1/analyze \
  -H "Content-Type: application/json" \
  -d '{"repository_url": "https://github.com/test/repo"}'

# Test with invalid token
curl -X POST https://api.ccl.dev/v1/analyze \
  -H "Authorization: Bearer invalid_token" \
  -H "Content-Type: application/json" \
  -d '{"repository_url": "https://github.com/test/repo"}'
```

## Success Metrics

### Performance Metrics
- **Response Time**: <100ms (p95)
- **Availability**: 99.9%
- **Throughput**: 1000+ requests/second
- **Error Rate**: <1%

### Security Metrics
- **Authentication Success Rate**: >99%
- **Authorization Accuracy**: 100%
- **Input Validation Coverage**: 100%
- **Security Scan Results**: No high/critical issues

### Developer Experience Metrics
- **API Documentation Completeness**: 100%
- **Error Message Clarity**: >90% helpful
- **SDK Integration Success**: >95%
- **Developer Satisfaction**: >4.5/5

## Final Validation Checklist
- [ ] All API endpoints implemented and tested
- [ ] Authentication and authorization working
- [ ] Input validation comprehensive
- [ ] Error handling consistent
- [ ] Rate limiting functional
- [ ] API documentation complete
- [ ] OpenAPI specification accurate
- [ ] Performance benchmarks met
- [ ] Security testing passed
- [ ] Webhook delivery working
- [ ] API versioning implemented
- [ ] Monitoring and alerting configured

---

## Implementation Notes

### API Design Best Practices
- Follow RESTful principles consistently
- Use proper HTTP status codes
- Implement comprehensive error handling
- Provide clear and helpful error messages

### Security Implementation
- Validate all input data thoroughly
- Implement proper authentication and authorization
- Use HTTPS for all communications
- Implement rate limiting and abuse prevention

### Performance Optimization
- Implement efficient database queries
- Use caching for frequently accessed data
- Optimize response payload sizes
- Monitor and optimize API performance continuously
