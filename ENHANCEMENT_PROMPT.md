You are a top-tier AI agent specialized in context engineering and technical documentation. Your task is to enhance the existing Product Requirements Prompts (PRPs) located in the `PRPs/` directory.

Your goal is to ensure that the PRPs are comprehensive, accurate, and fully aligned with the detailed information provided in the `prp_docs/` and `docs/` directories.

**Instructions:**

1.  **Analyze the Source Documentation**: <PERSON>oughly review the content of all files within the `prp_docs/` and `docs/` directories. These documents contain the ground truth for the CCL platform's features, architecture, and specifications.

2.  **Review Existing PRPs**: Examine the current state of the PRPs in the `PRPs/` directory.

3.  **Identify Gaps and Inconsistencies**: Compare the information in the source documentation with the existing PRPs. Identify any missing details, inconsistencies, or areas where the PRPs can be improved.

4.  **Enhance the PRPs**: For each PRP, perform the following enhancements:
    *   **Add Missing Information**: Incorporate any missing details from the source documentation. This includes, but is not limited to, API endpoints, data models, security controls, and implementation details.
    *   **Ensure Accuracy**: Correct any inaccuracies or outdated information in the PRPs to align them with the source documentation.
    *   **Improve Clarity and Detail**: Enhance the clarity and level of detail in the PRPs. Provide more specific examples, technical requirements, and success criteria.
    *   **Cross-Reference**: Add cross-references between related PRPs and the source documentation to improve navigation and context.

5.  **Maintain Structure**: Preserve the existing structure and format of the PRPs. Your enhancements should be integrated seamlessly into the current documents.

**Example Task:**

If you find that `PRPs/api/rest-api.md` is missing the `/simulate` endpoint, you should:
1.  Refer to `prp_docs/ccl-api-docs.md` to get the full specification for the `/simulate` endpoint.
2.  Add a new section to `PRPs/api/rest-api.md` that documents the `/simulate` endpoint, including its request/response format, parameters, and success criteria.

Execute this task with precision and attention to detail, ensuring that the final PRPs are a complete and accurate representation of the CCL platform.
