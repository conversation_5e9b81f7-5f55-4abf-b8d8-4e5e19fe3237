{"permissions": {"allow": ["mcp__filesystem__read_multiple_files", "mcp__filesystem__directory_tree", "mcp__filesystem__read_file", "mcp__filesystem__write_file", "mcp__filesystem__create_directory", "Bash(grep:*)", "Bash(ls:*)", "Bash(find:*)", "<PERSON><PERSON>(cat:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(touch:*)", "<PERSON><PERSON>(python:*)", "<PERSON><PERSON>(python3:*)", "Bash(node:*)", "Bash(npm:*)", "Bash(cargo:*)", "<PERSON><PERSON>(go:*)", "Bash(git:*)", "<PERSON><PERSON>(docker:*)", "<PERSON><PERSON>(gcloud:*)", "Bash(terraform:*)", "<PERSON><PERSON>(make:*)", "WebFetch(domain:docs.anthropic.com)", "WebFetch(domain:cloud.google.com)", "WebFetch(domain:github.com)", "WebFetch(domain:docs.rs)", "WebFetch(domain:pypi.org)", "WebFetch(domain:npmjs.com)", "mcp__filesystem__list_allowed_directories", "mcp__filesystem__search_files", "mcp__filesystem__list_directory", "mcp__filesystem__get_file_info", "mcp__sequential-thinking__sequentialthinking", "mcp__sequential-thinking__sequentialthinking", "mcp__filesystem__edit_file"], "deny": ["Bash(rm:*)", "<PERSON><PERSON>(rmdir:*)", "<PERSON><PERSON>(sudo:*)", "<PERSON><PERSON>(su:*)"]}, "context_engineering": {"memory_management": {"enabled": true, "session_persistence": true, "context_caching": true, "memory_cleanup_interval": 86400}, "task_integration": {"enabled": true, "auto_task_detection": true, "progress_tracking": true, "completion_validation": true}, "workflow_orchestration": {"enabled": true, "intelligent_suggestions": true, "automated_validation": true, "context_switching": true}, "prp_integration": {"enabled": true, "auto_prp_loading": true, "context_expansion": true, "validation_tracking": true}}, "development_environment": {"auto_setup": true, "tool_validation": true, "environment_monitoring": true, "performance_tracking": true}}