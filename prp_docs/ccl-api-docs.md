# CCL API Documentation
## Complete API Reference

**Version:** 1.0  
**Base URL:** `https://api.ccl.dev/v1`  
**Protocol:** HTTPS only  
**Format:** JSON (application/json)

---

## Authentication

CCL uses API key authentication. Include your API key in the Authorization header:

```http
Authorization: Bearer YOUR_API_KEY
```

### API Key Scopes

| Scope | Description | Endpoints |
|-------|-------------|-----------|
| `read:analysis` | Read analysis results | GET /analysis/*, GET /patterns/* |
| `write:analysis` | Create analyses | POST /analyze |
| `read:patterns` | Read patterns | GET /patterns/*, GET /marketplace/* |
| `write:patterns` | Create/modify patterns | POST /patterns, PUT /patterns/* |
| `read:queries` | Read conversations | GET /conversations/* |
| `write:queries` | Create queries | POST /query, POST /conversations/* |
| `admin` | Full access | All endpoints |

### OAuth 2.0 Flow

For web applications, use OAuth 2.0:

```
1. Redirect to: https://auth.ccl.dev/oauth/authorize
   ?client_id=YOUR_CLIENT_ID
   &redirect_uri=YOUR_REDIRECT_URI
   &response_type=code
   &scope=read:analysis write:analysis
   &state=RANDOM_STATE

2. User authorizes, redirected to:
   YOUR_REDIRECT_URI?code=AUTH_CODE&state=RANDOM_STATE

3. Exchange code for token:
   POST https://auth.ccl.dev/oauth/token
   {
     "grant_type": "authorization_code",
     "code": "AUTH_CODE",
     "client_id": "YOUR_CLIENT_ID",
     "client_secret": "YOUR_CLIENT_SECRET"
   }

4. Receive access token:
   {
     "access_token": "ACCESS_TOKEN",
     "token_type": "Bearer",
     "expires_in": 3600,
     "refresh_token": "REFRESH_TOKEN"
   }
```

---

## Rate Limiting

Rate limits are enforced per API key:

| Tier | Requests/Hour | Burst | Concurrent |
|------|---------------|-------|------------|
| Free | 1,000 | 50 | 10 |
| Pro | 10,000 | 500 | 50 |
| Team | 100,000 | 5,000 | 200 |
| Enterprise | Unlimited | Unlimited | Unlimited |

### Rate Limit Headers

```http
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 1640995200
X-RateLimit-Reset-After: 3600
```

### Rate Limit Response

```http
HTTP/1.1 429 Too Many Requests
Content-Type: application/json
Retry-After: 58

{
  "error": {
    "code": "RATE_LIMITED",
    "message": "Rate limit exceeded",
    "retry_after": 58,
    "limit": 1000,
    "reset_at": "2025-01-15T12:00:00Z"
  }
}
```

---

## Common Headers

### Request Headers

| Header | Required | Description |
|--------|----------|-------------|
| `Authorization` | Yes | Bearer token |
| `Content-Type` | Yes* | application/json (*for POST/PUT) |
| `Accept` | No | application/json (default) |
| `X-Request-ID` | No | Unique request identifier |
| `X-CCL-Version` | No | API version override |

### Response Headers

| Header | Description |
|--------|-------------|
| `X-Request-ID` | Request identifier for support |
| `X-Response-Time` | Processing time in ms |
| `X-CCL-Version` | API version used |
| `Cache-Control` | Caching directives |
| `ETag` | Resource version |

---

## Endpoints

### Analysis

#### Start Analysis

```http
POST /analyze
Content-Type: application/json

{
  "repository_url": "https://github.com/facebook/react",
  "branch": "main",
  "commit": "abc123",  // Optional: specific commit
  "languages": ["javascript", "typescript"],
  "options": {
    "incremental": false,
    "deep_pattern_analysis": true,
    "generate_embeddings": true,
    "include_paths": ["src", "lib"],
    "exclude_paths": ["node_modules", "test", "dist"],
    "pattern_confidence_threshold": 0.8
  },
  "webhook_url": "https://your-app.com/webhooks/ccl"
}
```

**Response:**
```json
{
  "analysis_id": "ana_1234567890abcdef",
  "status": "pending",
  "created_at": "2025-01-15T10:00:00Z",
  "estimated_completion": "2025-01-15T10:05:00Z",
  "repository": {
    "id": "repo_0987654321fedcba",
    "name": "facebook/react",
    "url": "https://github.com/facebook/react"
  },
  "webhook_url": "https://your-app.com/webhooks/ccl"
}
```

#### Get Analysis Status

```http
GET /analysis/{analysis_id}
```

**Response:**
```json
{
  "analysis_id": "ana_1234567890abcdef",
  "status": "completed",
  "started_at": "2025-01-15T10:00:00Z",
  "completed_at": "2025-01-15T10:04:32Z",
  "duration_seconds": 272,
  "repository": {
    "id": "repo_0987654321fedcba",
    "name": "facebook/react",
    "url": "https://github.com/facebook/react",
    "branch": "main",
    "commit": "abc123def456"
  },
  "statistics": {
    "files_analyzed": 2847,
    "total_lines": 385293,
    "patterns_detected": 156,
    "languages": {
      "javascript": 65.3,
      "typescript": 28.4,
      "css": 4.2,
      "other": 2.1
    },
    "components_identified": 234,
    "complexity_score": 7.2
  },
  "results": {
    "architecture": {
      "type": "component-based",
      "framework": "react",
      "patterns": ["flux", "hooks", "context"],
      "layers": ["components", "hooks", "utils", "core"]
    },
    "quality_metrics": {
      "maintainability_index": 78.5,
      "technical_debt_ratio": 0.12,
      "code_duplication": 2.3
    }
  }
}
```

#### List Analyses

```http
GET /analysis?repository_id={repo_id}&status=completed&limit=20&offset=0
```

**Response:**
```json
{
  "analyses": [
    {
      "analysis_id": "ana_1234567890abcdef",
      "repository_id": "repo_0987654321fedcba",
      "status": "completed",
      "started_at": "2025-01-15T10:00:00Z",
      "completed_at": "2025-01-15T10:04:32Z",
      "commit": "abc123def456",
      "summary": {
        "files": 2847,
        "patterns": 156,
        "duration": 272
      }
    }
  ],
  "pagination": {
    "total": 145,
    "limit": 20,
    "offset": 0,
    "has_more": true
  }
}
```

### Patterns

#### List Patterns

```http
GET /patterns?repository_id={repo_id}&type=design&min_confidence=0.8&limit=50
```

**Query Parameters:**
| Parameter | Type | Description |
|-----------|------|-------------|
| `repository_id` | string | Filter by repository |
| `type` | string | Pattern type: architectural, design, idiom |
| `language` | string | Programming language |
| `min_confidence` | float | Minimum confidence score (0-1) |
| `name` | string | Search by pattern name |
| `limit` | integer | Results per page (max 100) |
| `offset` | integer | Pagination offset |

**Response:**
```json
{
  "patterns": [
    {
      "id": "pat_abc123",
      "repository_id": "repo_0987654321fedcba",
      "type": "design",
      "name": "Repository Pattern",
      "description": "Data access abstraction layer",
      "confidence": 0.95,
      "occurrences": 23,
      "first_seen": "2025-01-10T08:00:00Z",
      "last_seen": "2025-01-15T10:00:00Z",
      "locations": [
        {
          "file": "src/repositories/UserRepository.js",
          "lines": [10, 145],
          "snippet": "class UserRepository extends BaseRepository {"
        }
      ],
      "template": {
        "code": "class ${EntityName}Repository extends BaseRepository {\n  async findById(id) {}\n  async findAll() {}\n  async create(data) {}\n  async update(id, data) {}\n  async delete(id) {}\n}",
        "variables": ["EntityName"],
        "language": "javascript"
      },
      "relationships": {
        "uses": ["pat_def456"],
        "used_by": ["pat_ghi789"]
      },
      "quality_score": 9.2,
      "tags": ["data-access", "abstraction", "solid"]
    }
  ],
  "pagination": {
    "total": 156,
    "limit": 50,
    "offset": 0,
    "has_more": true
  }
}
```

#### Get Pattern Details

```http
GET /patterns/{pattern_id}
```

**Response includes full pattern details plus:**
```json
{
  "examples": [
    {
      "file": "src/repositories/UserRepository.js",
      "code": "// Full implementation example",
      "quality_score": 9.5
    }
  ],
  "violations": [
    {
      "file": "src/services/OldUserService.js",
      "issue": "Direct database access instead of repository",
      "severity": "medium",
      "suggestion": "Refactor to use UserRepository"
    }
  ],
  "evolution": [
    {
      "version": 1,
      "date": "2024-06-15",
      "changes": "Initial pattern detection"
    },
    {
      "version": 2,
      "date": "2024-09-20",
      "changes": "Added async/await support"
    }
  ]
}
```

#### Create Pattern

```http
POST /patterns
Content-Type: application/json

{
  "name": "Custom Service Pattern",
  "description": "Our team's service layer implementation",
  "type": "architectural",
  "language": "javascript",
  "template": {
    "code": "class ${ServiceName}Service {\n  constructor(${dependencies}) {\n    ${initCode}\n  }\n  \n  ${methods}\n}",
    "variables": {
      "ServiceName": {
        "type": "string",
        "pattern": "^[A-Z][a-zA-Z]+$",
        "description": "Service class name"
      },
      "dependencies": {
        "type": "array",
        "description": "Constructor dependencies"
      }
    }
  },
  "examples": [
    {
      "name": "UserService",
      "code": "class UserService {\n  constructor(userRepo, emailService) {\n    this.userRepo = userRepo;\n    this.emailService = emailService;\n  }\n}"
    }
  ],
  "detection_rules": {
    "must_have": ["class", "Service", "constructor"],
    "must_not_have": ["extends Component", "render()"],
    "file_pattern": "src/services/*.js"
  },
  "tags": ["service", "business-logic", "custom"]
}
```

#### Validate Code Against Patterns

```http
POST /patterns/validate
Content-Type: application/json

{
  "repository_id": "repo_0987654321fedcba",
  "code": "class UserService {\n  async getUser(id) {\n    return db.query('SELECT * FROM users WHERE id = ?', id);\n  }\n}",
  "file_path": "src/services/UserService.js",
  "pattern_ids": ["pat_abc123"],  // Optional: specific patterns to check
  "strict": true
}
```

**Response:**
```json
{
  "valid": false,
  "confidence": 0.85,
  "violations": [
    {
      "pattern_id": "pat_abc123",
      "pattern_name": "Repository Pattern",
      "type": "violation",
      "severity": "high",
      "line": 3,
      "column": 12,
      "message": "Direct database access detected. Should use UserRepository",
      "suggestion": "return this.userRepository.findById(id);",
      "auto_fixable": true
    }
  ],
  "suggestions": [
    {
      "pattern_id": "pat_def456",
      "pattern_name": "Error Handling Pattern",
      "type": "missing",
      "severity": "medium",
      "message": "No error handling for database query",
      "suggestion": "Wrap in try-catch block"
    }
  ]
}
```

### Queries

#### Execute Query

```http
POST /query
Content-Type: application/json

{
  "query": "How does the authentication system work?",
  "repository_id": "repo_0987654321fedcba",
  "conversation_id": "conv_xyz789",  // Optional: maintain context
  "context": {
    "focus_area": "security",
    "include_code": true,
    "max_examples": 3
  },
  "stream": false,  // Set true for SSE streaming
  "model": "advanced"  // or "fast"
}
```

**Response (non-streaming):**
```json
{
  "answer": "The authentication system uses a JWT-based approach...",
  "confidence": 0.92,
  "conversation_id": "conv_xyz789",
  "sources": [
    {
      "file": "src/auth/AuthService.js",
      "lines": [45, 89],
      "relevance": 0.95,
      "snippet": "generateToken(user) {\n  return jwt.sign(..."
    }
  ],
  "code_examples": [
    {
      "title": "JWT Token Generation",
      "file": "src/auth/AuthService.js",
      "code": "// Full code example here",
      "language": "javascript"
    }
  ],
  "patterns_referenced": [
    {
      "id": "pat_auth123",
      "name": "JWT Authentication Pattern",
      "relevance": 0.88
    }
  ],
  "suggested_questions": [
    "How is token refresh handled?",
    "What about role-based access control?",
    "Where are tokens stored on the client?"
  ],
  "metadata": {
    "processing_time_ms": 1823,
    "tokens_used": 4521,
    "cache_hit": false
  }
}
```

**Streaming Response (SSE):**
```
data: {"type": "start", "conversation_id": "conv_xyz789"}

data: {"type": "chunk", "content": "The authentication system "}

data: {"type": "chunk", "content": "uses a JWT-based approach"}

data: {"type": "source", "source": {"file": "src/auth/AuthService.js", "lines": [45, 89]}}

data: {"type": "done", "suggested_questions": [...], "metadata": {...}}
```

#### Conversation Management

```http
POST /conversations
Content-Type: application/json

{
  "repository_id": "repo_0987654321fedcba",
  "title": "Authentication Deep Dive",
  "context": {
    "user_role": "senior_developer",
    "focus_areas": ["security", "performance"]
  }
}
```

```http
GET /conversations/{conversation_id}
```

```http
POST /conversations/{conversation_id}/messages
Content-Type: application/json

{
  "message": "What about OAuth integration?",
  "include_code": true
}
```

```http
GET /conversations/{conversation_id}/messages?limit=50
```

### Repositories

#### Create Repository

```http
POST /repositories
Content-Type: application/json

{
  "url": "https://github.com/your/repo",
  "name": "My Project",
  "branch": "main",
  "provider": "github",
  "authentication": {
    "type": "token",
    "token": "ghp_xxxxxxxxxxxx"  // For private repos
  },
  "auto_analyze": true,
  "analysis_schedule": "0 0 * * *"  // Daily at midnight
}
```

#### Get Repository

```http
GET /repositories/{repository_id}
```

**Response:**
```json
{
  "id": "repo_0987654321fedcba",
  "name": "facebook/react",
  "url": "https://github.com/facebook/react",
  "provider": "github",
  "branch": "main",
  "created_at": "2025-01-10T08:00:00Z",
  "last_analysis": {
    "id": "ana_1234567890abcdef",
    "completed_at": "2025-01-15T10:04:32Z",
    "status": "completed",
    "commit": "abc123def456"
  },
  "statistics": {
    "total_files": 2847,
    "total_lines": 385293,
    "primary_language": "javascript",
    "languages": {
      "javascript": 65.3,
      "typescript": 28.4,
      "css": 4.2,
      "other": 2.1
    },
    "contributors": 1823,
    "age_days": 3285
  },
  "settings": {
    "auto_analyze": true,
    "analysis_schedule": "0 0 * * *",
    "webhook_url": "https://your-app.com/webhooks/ccl"
  }
}
```

#### Update Repository

```http
PATCH /repositories/{repository_id}
Content-Type: application/json

{
  "branch": "develop",
  "auto_analyze": false,
  "settings": {
    "exclude_paths": ["vendor", "node_modules"]
  }
}
```

#### Delete Repository

```http
DELETE /repositories/{repository_id}
```

### Marketplace

#### Search Marketplace

```http
GET /marketplace/patterns?q=authentication&category=security&language=javascript&sort=popular
```

**Query Parameters:**
| Parameter | Type | Description |
|-----------|------|-------------|
| `q` | string | Search query |
| `category` | string | Category filter |
| `language` | string | Language filter |
| `author` | string | Author filter |
| `min_rating` | float | Minimum rating (1-5) |
| `price_range` | string | free, paid, 0-10, 10-50, 50+ |
| `sort` | string | popular, recent, rating, price_asc, price_desc |

**Response:**
```json
{
  "patterns": [
    {
      "id": "mkt_pat_123",
      "name": "Advanced JWT Authentication",
      "description": "Production-ready JWT implementation with refresh tokens",
      "author": {
        "id": "usr_456",
        "name": "Jane Developer",
        "verified": true
      },
      "price": 9.99,
      "currency": "USD",
      "rating": 4.8,
      "reviews": 234,
      "downloads": 5678,
      "updated_at": "2025-01-10T12:00:00Z",
      "preview": {
        "template_snippet": "// Preview code",
        "examples": 3,
        "languages": ["javascript", "typescript"]
      },
      "tags": ["auth", "jwt", "security", "production-ready"]
    }
  ],
  "facets": {
    "categories": {
      "security": 145,
      "data-access": 89,
      "api": 67
    },
    "price_ranges": {
      "free": 234,
      "0-10": 156,
      "10-50": 78,
      "50+": 23
    }
  },
  "pagination": {
    "total": 491,
    "limit": 20,
    "offset": 0
  }
}
```

#### Get Marketplace Pattern

```http
GET /marketplace/patterns/{pattern_id}
```

#### Purchase Pattern

```http
POST /marketplace/purchase
Content-Type: application/json

{
  "pattern_id": "mkt_pat_123",
  "payment_method_id": "pm_1234567890",  // Stripe payment method
  "license_type": "team"  // or "individual", "enterprise"
}
```

**Response:**
```json
{
  "purchase_id": "pur_abc123",
  "pattern_id": "mkt_pat_123",
  "status": "completed",
  "amount": 9.99,
  "currency": "USD",
  "license": {
    "type": "team",
    "seats": 10,
    "expires_at": null,
    "key": "LIC-XXXX-XXXX-XXXX"
  },
  "download_url": "https://api.ccl.dev/v1/downloads/pur_abc123",
  "receipt_url": "https://ccl.dev/receipts/pur_abc123"
}
```

#### Publish Pattern

```http
POST /marketplace/patterns
Content-Type: application/json

{
  "name": "Microservices Communication Pattern",
  "description": "Battle-tested patterns for service-to-service communication",
  "category": "architecture",
  "price": 19.99,
  "license": "single-use",
  "pattern": {
    // Pattern definition (same as POST /patterns)
  },
  "documentation": "# Comprehensive docs here",
  "demo_repository": "https://github.com/author/pattern-demo",
  "support_email": "<EMAIL>"
}
```

### Code Generation

#### Generate Code from Pattern

```http
POST /generate
Content-Type: application/json

{
  "pattern_id": "pat_abc123",
  "variables": {
    "EntityName": "Order",
    "fields": ["id", "customer_id", "total", "status"]
  },
  "options": {
    "include_tests": true,
    "include_documentation": true,
    "style_guide": "repository_style_guide_v2"
  }
}
```

**Response:**
```json
{
  "generated_code": {
    "main": {
      "filename": "OrderRepository.js",
      "code": "// Generated implementation",
      "language": "javascript"
    },
    "tests": {
      "filename": "OrderRepository.test.js",
      "code": "// Generated tests",
      "language": "javascript"
    },
    "documentation": {
      "filename": "OrderRepository.md",
      "content": "# OrderRepository Documentation"
    }
  },
  "integration_instructions": [
    "1. Create file at src/repositories/OrderRepository.js",
    "2. Update dependency injection in src/config/dependencies.js",
    "3. Run tests with npm test"
  ]
}
```

### Simulations

#### Simulate Architecture Change

```http
POST /simulate
Content-Type: application/json

{
  "repository_id": "repo_0987654321fedcba",
  "simulation_type": "refactoring",
  "changes": {
    "description": "Convert monolith to microservices",
    "target_services": [
      {
        "name": "AuthService",
        "components": ["auth", "users", "permissions"]
      },
      {
        "name": "PaymentService",
        "components": ["payments", "billing", "invoices"]
      }
    ],
    "constraints": {
      "preserve_api": true,
      "max_downtime_hours": 2,
      "gradual_migration": true
    }
  }
}
```

**Response:**
```json
{
  "simulation_id": "sim_xyz123",
  "feasibility": 0.85,
  "estimated_effort": {
    "developer_days": 45,
    "calendar_days": 21,
    "team_size": 3
  },
  "risk_assessment": {
    "overall_risk": "medium",
    "factors": [
      {
        "factor": "Data consistency",
        "risk": "high",
        "mitigation": "Implement distributed transactions"
      }
    ]
  },
  "migration_plan": {
    "phases": [
      {
        "phase": 1,
        "name": "Extract Authentication",
        "duration_days": 7,
        "tasks": [
          "Create AuthService repository",
          "Move auth components",
          "Update API gateway"
        ]
      }
    ]
  },
  "impact_analysis": {
    "affected_files": 234,
    "affected_tests": 567,
    "api_changes": 12,
    "breaking_changes": 0
  }
}
```

### Webhooks

#### Webhook Events

CCL sends webhooks for the following events:

| Event | Description | Payload |
|-------|-------------|---------|
| `analysis.started` | Analysis began | Analysis object |
| `analysis.completed` | Analysis finished | Full analysis results |
| `analysis.failed` | Analysis error | Error details |
| `pattern.detected` | New pattern found | Pattern object |
| `repository.updated` | Repository changed | Repository object |

#### Webhook Payload

```json
{
  "id": "evt_abc123",
  "type": "analysis.completed",
  "created": "2025-01-15T10:04:32Z",
  "data": {
    // Event-specific data
  }
}
```

#### Webhook Security

Verify webhook signatures:

```javascript
const crypto = require('crypto');

function verifyWebhook(payload, signature, secret) {
  const expected = crypto
    .createHmac('sha256', secret)
    .update(payload)
    .digest('hex');
  
  return crypto.timingSafeEqual(
    Buffer.from(signature),
    Buffer.from(`sha256=${expected}`)
  );
}
```

---

## Error Responses

### Error Format

```json
{
  "error": {
    "code": "INVALID_REQUEST",
    "message": "Human-readable error message",
    "field": "repository_url",  // If field-specific
    "details": {
      // Additional error context
    }
  },
  "request_id": "req_abc123xyz"
}
```

### Error Codes

| Code | HTTP Status | Description |
|------|-------------|-------------|
| `INVALID_REQUEST` | 400 | Malformed request |
| `VALIDATION_ERROR` | 400 | Invalid field values |
| `AUTHENTICATION_REQUIRED` | 401 | Missing authentication |
| `INVALID_API_KEY` | 401 | Invalid or expired API key |
| `INSUFFICIENT_PERMISSIONS` | 403 | Lacks required scope |
| `NOT_FOUND` | 404 | Resource not found |
| `CONFLICT` | 409 | Resource conflict |
| `RATE_LIMITED` | 429 | Too many requests |
| `INTERNAL_ERROR` | 500 | Server error |
| `SERVICE_UNAVAILABLE` | 503 | Temporary outage |

### Field Validation Errors

```json
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid request parameters",
    "validation_errors": [
      {
        "field": "repository_url",
        "code": "invalid_format",
        "message": "Must be a valid Git URL"
      },
      {
        "field": "languages[2]",
        "code": "unsupported",
        "message": "Language 'cobol' is not supported"
      }
    ]
  }
}
```

---

## Pagination

### Offset-based Pagination

```http
GET /patterns?limit=20&offset=40
```

```json
{
  "data": [...],
  "pagination": {
    "total": 156,
    "limit": 20,
    "offset": 40,
    "has_more": true,
    "links": {
      "first": "/patterns?limit=20&offset=0",
      "prev": "/patterns?limit=20&offset=20",
      "next": "/patterns?limit=20&offset=60",
      "last": "/patterns?limit=20&offset=140"
    }
  }
}
```

### Cursor-based Pagination

```http
GET /conversations/{id}/messages?limit=50&cursor=msg_xyz789
```

```json
{
  "data": [...],
  "pagination": {
    "limit": 50,
    "has_more": true,
    "cursor": {
      "next": "msg_abc123",
      "prev": "msg_def456"
    }
  }
}
```

---

## Versioning

### API Version

The API version is included in the URL path:
- Current: `/v1`
- Beta: `/v2-beta`

### Version Lifecycle

| Version | Status | Support End |
|---------|--------|-------------|
| v1 | Stable | 2027-01-01 |
| v2-beta | Beta | - |

### Breaking Changes

Breaking changes require a new major version:
- Removing endpoints
- Changing required parameters
- Modifying response structure
- Altering authentication

### Deprecation Policy

1. Announce deprecation 6 months in advance
2. Add `Deprecation` header to responses
3. Email affected users
4. Provide migration guide
5. Maintain deprecated version for 12 months

---

## SDKs and Libraries

### Official SDKs

| Language | Package | Documentation |
|----------|---------|---------------|
| JavaScript/Node | `@ccl/sdk` | [npm](https://npmjs.com/@ccl/sdk) |
| Python | `ccl-sdk` | [pypi](https://pypi.org/project/ccl-sdk) |
| Go | `github.com/ccl-platform/ccl-go` | [pkg.go.dev](https://pkg.go.dev/github.com/ccl-platform/ccl-go) |
| Java | `dev.ccl:ccl-sdk` | [Maven Central](https://central.sonatype.com/) |
| Ruby | `ccl` | [RubyGems](https://rubygems.org/gems/ccl) |
| PHP | `ccl/ccl-php` | [Packagist](https://packagist.org/packages/ccl/ccl-php) |
| .NET | `CCL.SDK` | [NuGet](https://www.nuget.org/packages/CCL.SDK) |

### Community SDKs

- Rust: `ccl-rs`
- Swift: `CCLSwift`
- Kotlin: `ccl-kotlin`
- Dart: `ccl_dart`

---

## Testing

### Test Environment

Base URL: `https://api-test.ccl.dev/v1`

Test API Keys:
- Free tier: `test_free_xxxxxxxxxxxx`
- Pro tier: `test_pro_xxxxxxxxxxxx`
- Team tier: `test_team_xxxxxxxxxxxx`

### Test Data

Pre-populated test repositories:
- `test_repo_small` - 1k lines
- `test_repo_medium` - 100k lines
- `test_repo_large` - 1M lines

### Webhook Testing

Test webhook endpoint: `https://webhook-test.ccl.dev/YOUR_KEY`

---

## Support

### Resources

- API Status: [status.ccl.dev](https://status.ccl.dev)
- Developer Forum: [forum.ccl.dev](https://forum.ccl.dev)
- GitHub Issues: [github.com/ccl-platform/issues](https://github.com/ccl-platform/issues)
- Email: <EMAIL>

### SLA

| Tier | Uptime SLA | Support Response |
|------|------------|------------------|
| Free | Best effort | Community |
| Pro | 99.9% | 24 hours |
| Team | 99.95% | 4 hours |
| Enterprise | 99.99% | 1 hour |

---

## Changelog

### v1.0.0 (2025-01-15)
- Initial release
- Core analysis endpoints
- Pattern detection
- Natural language queries
- Marketplace integration

### Upcoming (v2.0.0)
- GraphQL API
- Real-time subscriptions
- Advanced simulations
- Cross-repository analysis
- Custom model training