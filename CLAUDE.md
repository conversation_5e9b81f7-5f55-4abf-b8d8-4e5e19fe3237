# 🔄 Project Awareness & Context

## Always Reference These Files
- **Read `PLANNING.md`** at conversation start for architecture, goals, and constraints
- **Check `TASK.md`** before any work - add new tasks with date if not listed
- **Consult `PRPs/implementation-guide.md`** for detailed code patterns and examples
- **Review `PRPs/architecture-patterns.md`** for architectural patterns and principles
- **Check `PRPs/feature-specifications.md`** for feature specifications and requirements
- **Use `INITIAL.md`** as template for new feature requests

## CCL Project Overview
CCL (Codebase Context Layer) is a cloud-native, AI-powered platform for codebase intelligence. You're working on a multi-service architecture using:
- **Rust** (analysis-engine): AST parsing and code analysis
- **Python** (query-intelligence, pattern-mining): AI/ML operations
- **Go** (marketplace): High-performance API services
- **TypeScript** (web, sdk): Frontend and client libraries

# 🏗️ Architecture & Service Boundaries

## Service Responsibilities (NEVER MIX)
- `analysis-engine/`: Code parsing, AST analysis (Rust only)
- `query-intelligence/`: Natural language processing (Python only)
- `pattern-mining/`: Pattern detection and ML (Python only)
- `marketplace/`: Commerce and distribution (Go only)
- `web/`: Frontend UI (TypeScript/React only)
- `sdk/`: Client libraries (TypeScript only)

## Google Cloud Platform Standards
```yaml
Required Services:
  - Spanner: Transactional data (patterns, users, billing)
  - BigQuery: Analytics and aggregations
  - Firestore: Real-time collaboration data
  - Cloud Storage: Code artifacts and analysis results
  - Vertex AI: ML model training and inference
  - Cloud Run: All service deployments
  - Pub/Sub: Event-driven communication
```

# 🧱 Code Structure & Modularity

## File Organization
- **Never exceed 500 lines** per file - split into modules
- **Service structure**:
  ```
  service-name/
  ├── cmd/               # Entry points
  ├── internal/          # Private implementation
  ├── pkg/              # Public packages
  ├── api/              # API definitions (proto/openapi)
  ├── tests/            # All test files
  └── docs/             # Service documentation
  ```

## Language-Specific Patterns

### Rust (Analysis Engine)
```rust
// Always use Result types with custom errors
pub fn analyze_codebase(path: &Path) -> Result<Analysis, AnalysisError>

// Use async/await for I/O
// Use rayon for CPU-bound parallelism
// Prefer &str over String for inputs
```

### Python (AI Services)
```python
# Always use type hints
from typing import List, Dict, Optional

# Use dataclasses for models
@dataclass
class QueryResult:
    answer: str
    confidence: float
    references: List[CodeReference]

# Async by default
async def process_query(query: str) -> QueryResult:
```

### Go (Marketplace)
```go
// Follow standard project layout
// Always use context.Context
func (s *Service) GetPattern(ctx context.Context, id string) (*Pattern, error)

// Return errors, don't panic
// Use structured logging
```

### TypeScript (Web/SDK)
```typescript
// Strict mode always
// No any types
interface Pattern {
  id: string;
  name: string;
  // Full typing required
}

// React: functional components only
// Use React Query for data fetching
```

# 🧪 Testing & Reliability

## Testing Requirements
- **Unit tests**: Minimum 90% coverage
- **Integration tests**: All API endpoints
- **E2E tests**: Critical user journeys
- Test file naming: `*_test.{ext}` alongside source

## Test Structure
```python
# Example Python test structure
async def test_query_processor_success():
    # Arrange
    processor = QueryProcessor()
    query = "Find authentication patterns"
    
    # Act
    result = await processor.process(query)
    
    # Assert
    assert result.confidence > 0.8
    assert len(result.references) > 0
```

# ✅ Task Management

## Task Workflow
1. Check `TASK.md` for current work
2. Update task status when starting
3. Mark complete immediately when done
4. Add discovered tasks under "Discovered During Work"

## Git Workflow
```bash
# Branch naming
feature/analysis-api-implementation
fix/memory-leak-pattern-detector
docs/api-authentication-guide

# Commit format (conventional commits)
feat: add repository analysis endpoint
fix: resolve WebSocket connection timeout
docs: update SDK installation guide
test: add pattern validation tests
refactor: optimize AST traversal

# Always squash merge to main
```

# 📎 Development Standards

## API Design
- REST for external APIs
- gRPC for internal service communication
- GraphQL for flexible frontend queries
- OpenAPI 3.0 documentation required

## Error Handling
```python
# Always use custom exceptions
class PatternNotFoundError(CCLError):
    def __init__(self, pattern_id: str):
        super().__init__(
            message=f"Pattern {pattern_id} not found",
            error_code="PATTERN_NOT_FOUND",
            status_code=404
        )
```

## Performance Requirements
- Query response: <100ms (p95)
- Analysis: <5 minutes for 1M LOC
- Pattern detection: <30s for standard repo
- API rate limits:
  - Free: 1,000/hour
  - Pro: 10,000/hour
  - Team: 100,000/hour
  - Enterprise: Unlimited

# 🔐 Security Requirements

## Zero Trust Principles
- Authenticate every request
- Authorize at service boundaries
- Encrypt all data (transit & rest)
- Audit log everything

## Compliance Standards
- SOC2 Type II compliant
- HIPAA ready
- GDPR compliant
- CCPA compliant

## Secret Management
```bash
# NEVER hardcode secrets
# Use Secret Manager exclusively
gcloud secrets versions access latest --secret="api-key"

# Local development: .env files (git-ignored)
# Production: Secret Manager only
```

# 📚 Common Commands

## Development
```bash
# Local development
make dev-up              # Start all services locally
make dev-logs           # View service logs
make dev-down           # Stop services

# Testing
make test               # Run all tests
make test-service SERVICE=analysis-engine
make test-integration   # Integration tests only

# Building
make build-all          # Build all services
make build SERVICE=marketplace

# Linting
make lint               # Run all linters
make fmt               # Format code
```

## Deployment
```bash
# Deploy to environment
make deploy ENV=development
make deploy ENV=staging
make deploy ENV=production SERVICE=query-intelligence

# Infrastructure
cd infrastructure/
terraform plan -out=tfplan
terraform apply tfplan
```

# 🚨 Critical Anti-Patterns

## NEVER DO:
1. Direct database access across services
2. Synchronous calls for long operations
3. Store business logic in frontend
4. Manual scaling configurations
5. Custom authentication systems
6. Hardcode configuration values
7. Skip security reviews
8. Deploy without tests passing

## ALWAYS DO:
1. Use service APIs for cross-service data
2. Implement async patterns with Pub/Sub
3. Keep business logic server-side
4. Use autoscaling configurations
5. Use Identity Platform/Firebase Auth
6. Use environment variables/Secret Manager
7. Get security approval for changes
8. Ensure all tests pass before deploy

# 🛠️ Debugging & Monitoring

## Logging Standards
```python
import structlog
logger = structlog.get_logger()

# Always include context
logger.info(
    "query_processed",
    query_id=query_id,
    duration_ms=duration,
    confidence=result.confidence
)
```

## Distributed Tracing
- Use OpenTelemetry
- Propagate trace context
- Add custom spans for operations
- Include business metrics

## Error Investigation
1. Check Cloud Logging for errors
2. Review distributed traces
3. Check service health endpoints
4. Verify configuration values

# 📋 PR Requirements

## Before Submitting PR:
1. All tests pass
2. Coverage maintained >90%
3. No linting errors
4. Documentation updated
5. CHANGELOG entry added
6. Security scan passed

## PR Description Template:
```markdown
## What
Brief description of changes

## Why
Context and motivation

## How
Technical approach

## Testing
How it was tested

## Screenshots
(if UI changes)
```

# 🔗 Key Resources

## Internal
- Architecture: `docs/architecture/`
- API Specs: `api/openapi/` and `api/proto/`
- Runbooks: `docs/runbooks/`
- Security: `docs/security/`

## External
- [GCP Best Practices](https://cloud.google.com/docs/enterprise/best-practices-for-enterprise-organizations)
- [Vertex AI Docs](https://cloud.google.com/vertex-ai/docs)
- [Domain-Driven Design](https://martinfowler.com/bliki/DomainDrivenDesign.html)

---

Remember: CCL is an enterprise platform. Every change impacts real users. Maintain quality, security, and performance standards at all times.