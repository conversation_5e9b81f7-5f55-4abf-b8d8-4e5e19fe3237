# Archived Documentation

This directory contains documentation that has been migrated to the PRP (Product Requirements Prompts) system.

## Migration Status

### ✅ Migrated to PRPs

1. **Technical Specification** → Split into multiple PRPs:
   - Database schemas → `/PRPs/database/`
   - AI/ML implementation → `/PRPs/ai-ml/`
   - Language parsers → `/PRPs/development/language-parsers.md`
   - Service specifications → `/PRPs/services/`

2. **Developer Guide** → Split into:
   - Implementation patterns → `/PRPs/implementation-guide.md`
   - Development environment → `/PRPs/development/dev-environment.md`
   - Testing strategies → (To be created in `/PRPs/development/testing-strategy.md`)

3. **SDK Documentation** → To be migrated to:
   - `/PRPs/sdk/` (Per-language SDK PRPs)

### 📋 Pending Migration

- Security documentation → `/PRPs/security/`
- Business/marketing documentation → May not need PRP format
- API documentation → `/PRPs/api/`

## Why Archive?

These documents have been superseded by the PRP system, which provides:
- Better structure for AI-assisted development
- Self-contained, executable specifications
- Built-in validation and testing
- Clear implementation blueprints

## Accessing Archived Content

While the PRPs are the primary source of truth, these archived documents may be useful for:
- Historical reference
- Understanding original design decisions
- Cross-referencing during migration

**Note**: Always refer to the corresponding PRP for the most up-to-date information.